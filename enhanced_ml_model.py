"""
Modèle ML amélioré pour la prédiction Keno avec ensemble sophistiqué.
Améliore significativement les performances par rapport au modèle de base.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import pickle
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Imports ML avec gestion d'erreur
try:
    from catboost import CatBoostClassifier
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

try:
    from xgboost import XGBClassifier
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    from lightgbm import LGBMClassifier
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    from sklearn.ensemble import RandomForestClassifier, VotingClassifier, StackingClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.model_selection import cross_val_score, StratifiedKFold
    from sklearn.metrics import roc_auc_score, accuracy_score, precision_score, recall_score
    from sklearn.calibration import CalibratedClassifierCV
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

from feature_engineering import KenoFeatureEngineer

logger = logging.getLogger(__name__)

class EnhancedKenoMLModel:
    """Modèle ML amélioré avec ensemble sophistiqué pour la prédiction Keno."""
    
    def __init__(self, cache_dir="keno_data/cache"):
        """Initialise le modèle ML amélioré."""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Feature engineering amélioré
        self.feature_engineer = KenoFeatureEngineer(lookback_window=50)
        
        # Modèles d'ensemble
        self.ensemble_models = {}  # Un ensemble par numéro (1-70)
        self.base_models = {}      # Modèles de base pour chaque numéro
        self.meta_models = {}      # Méta-modèles pour le stacking
        
        # État d'entraînement
        self.is_trained = False
        self.training_metrics = {}
        self.feature_importance = {}
        
        # Configuration des modèles
        self.model_config = self._get_model_config()
        
        # Fichiers de sauvegarde
        self.models_file = self.cache_dir / "enhanced_ensemble_models.pkl"
        self.metrics_file = self.cache_dir / "enhanced_training_metrics.pkl"
        self.importance_file = self.cache_dir / "enhanced_feature_importance.pkl"
        
    def _get_model_config(self) -> Dict:
        """Configuration optimisée des modèles."""
        config = {
            'catboost': {
                'iterations': 500,
                'depth': 6,
                'learning_rate': 0.1,
                'l2_leaf_reg': 3,
                'border_count': 128,
                'thread_count': -1,
                'verbose': False,
                'random_seed': 42,
                'loss_function': 'Logloss',
                'eval_metric': 'AUC',
                'od_type': 'Iter',
                'od_wait': 50
            },
            'xgboost': {
                'n_estimators': 300,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'random_state': 42,
                'n_jobs': -1,
                'eval_metric': 'auc'
            },
            'lightgbm': {
                'n_estimators': 300,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'random_state': 42,
                'n_jobs': -1,
                'verbose': -1
            },
            'random_forest': {
                'n_estimators': 200,
                'max_depth': 10,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42,
                'n_jobs': -1
            },
            'logistic': {
                'random_state': 42,
                'max_iter': 1000,
                'n_jobs': -1
            }
        }
        return config
    
    def _create_base_models(self) -> List[Any]:
        """Crée les modèles de base pour l'ensemble."""
        models = []
        
        # CatBoost (prioritaire)
        if CATBOOST_AVAILABLE:
            models.append(('catboost', CatBoostClassifier(**self.model_config['catboost'])))
        
        # XGBoost
        if XGBOOST_AVAILABLE:
            models.append(('xgboost', XGBClassifier(**self.model_config['xgboost'])))
        
        # LightGBM
        if LIGHTGBM_AVAILABLE:
            models.append(('lightgbm', LGBMClassifier(**self.model_config['lightgbm'])))
        
        # Random Forest (toujours disponible)
        if SKLEARN_AVAILABLE:
            models.append(('rf', RandomForestClassifier(**self.model_config['random_forest'])))
        
        if not models:
            raise RuntimeError("Aucun modèle ML disponible. Installez catboost, xgboost ou scikit-learn.")
        
        logger.info(f"Modèles de base créés: {[name for name, _ in models]}")
        return models
    
    def _create_ensemble_model(self, base_models: List[Any]) -> Any:
        """Crée un modèle d'ensemble sophistiqué."""
        if not SKLEARN_AVAILABLE:
            # Fallback: utiliser seulement le premier modèle
            return base_models[0][1]
        
        if len(base_models) == 1:
            return base_models[0][1]
        
        # Stacking Classifier avec méta-modèle
        meta_model = LogisticRegression(**self.model_config['logistic'])
        
        try:
            ensemble = StackingClassifier(
                estimators=base_models,
                final_estimator=meta_model,
                cv=3,
                n_jobs=-1,
                passthrough=False
            )
            
            # Calibration pour améliorer les probabilités
            calibrated_ensemble = CalibratedClassifierCV(
                ensemble, 
                method='isotonic', 
                cv=3
            )
            
            return calibrated_ensemble
            
        except Exception as e:
            logger.warning(f"Erreur création ensemble, utilisation VotingClassifier: {e}")
            # Fallback: VotingClassifier
            return VotingClassifier(
                estimators=base_models,
                voting='soft',
                n_jobs=-1
            )
    
    def train_models(self, data: pd.DataFrame, max_samples: int = 10000, 
                    min_history: int = 100) -> Dict:
        """Entraîne les modèles d'ensemble améliorés."""
        logger.info("🚀 Début entraînement modèles ML améliorés...")
        
        if len(data) < min_history:
            raise ValueError(f"Données insuffisantes: {len(data)} < {min_history}")
        
        # Créer le dataset d'entraînement avec features améliorées
        logger.info("Création des features améliorées...")
        features_df, target_dfs = self.feature_engineer.create_training_dataset(
            data, min_history=min_history
        )
        
        if features_df is None or len(features_df) == 0:
            raise ValueError("Échec création dataset d'entraînement")
        
        # Limiter les échantillons si nécessaire
        if len(features_df) > max_samples:
            sample_indices = np.random.choice(len(features_df), max_samples, replace=False)
            features_df = features_df.iloc[sample_indices]
            target_dfs = {num: df.iloc[sample_indices] for num, df in target_dfs.items()}
        
        logger.info(f"Dataset: {len(features_df)} échantillons, {len(features_df.columns)} features")
        
        # Entraîner un ensemble pour chaque numéro
        training_results = {}
        successful_models = 0

        for num in range(1, 71):
            # Vérifier si le numéro existe dans les targets
            if str(num) not in target_dfs:
                continue

            # Vérifier si le DataFrame est vide
            if len(target_dfs[str(num)]) == 0:
                continue
                
            try:
                logger.info(f"Entraînement ensemble pour numéro {num}...")
                
                # Préparer les données
                X = features_df.values
                y = target_dfs[str(num)].values.ravel()
                
                # Vérifier l'équilibre des classes
                positive_rate = np.mean(y)
                if positive_rate < 0.01 or positive_rate > 0.99:
                    logger.warning(f"Numéro {num}: classes déséquilibrées ({positive_rate:.3f})")
                
                # Créer et entraîner l'ensemble
                base_models = self._create_base_models()
                ensemble_model = self._create_ensemble_model(base_models)
                
                # Entraînement avec validation croisée
                cv_scores = []
                if SKLEARN_AVAILABLE and len(np.unique(y)) > 1:
                    try:
                        cv = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
                        cv_scores = cross_val_score(ensemble_model, X, y, cv=cv, scoring='roc_auc', n_jobs=-1)
                    except Exception as e:
                        logger.warning(f"Validation croisée échouée pour {num}: {e}")
                
                # Entraînement final
                ensemble_model.fit(X, y)
                
                # Évaluation
                y_pred = ensemble_model.predict(X)
                y_proba = ensemble_model.predict_proba(X)[:, 1] if hasattr(ensemble_model, 'predict_proba') else y_pred
                
                metrics = {
                    'accuracy': accuracy_score(y, y_pred),
                    'auc': roc_auc_score(y, y_proba) if len(np.unique(y)) > 1 else 0.5,
                    'precision': precision_score(y, y_pred, zero_division=0),
                    'recall': recall_score(y, y_pred, zero_division=0),
                    'positive_rate': positive_rate,
                    'cv_auc_mean': np.mean(cv_scores) if cv_scores else 0,
                    'cv_auc_std': np.std(cv_scores) if cv_scores else 0,
                    'train_samples': len(X)
                }
                
                # Sauvegarder le modèle et les métriques
                self.ensemble_models[num] = ensemble_model
                self.training_metrics[num] = metrics
                training_results[num] = metrics
                successful_models += 1
                
                # Feature importance si disponible
                if hasattr(ensemble_model, 'feature_importances_'):
                    self.feature_importance[num] = ensemble_model.feature_importances_
                
                logger.info(f"✅ Numéro {num}: AUC={metrics['auc']:.3f}, Acc={metrics['accuracy']:.3f}")
                
            except Exception as e:
                logger.error(f"❌ Erreur entraînement numéro {num}: {e}")
                continue
        
        # Finaliser l'entraînement
        if successful_models > 0:
            self.is_trained = True
            self.save_models()
            
            # Statistiques globales
            all_aucs = [m['auc'] for m in training_results.values()]
            all_accs = [m['accuracy'] for m in training_results.values()]
            
            summary = {
                'models_trained': successful_models,
                'total_models': 70,
                'success_rate': successful_models / 70,
                'average_auc': np.mean(all_aucs),
                'average_accuracy': np.mean(all_accs),
                'auc_std': np.std(all_aucs),
                'accuracy_std': np.std(all_accs),
                'training_time': datetime.now().isoformat(),
                'dataset_size': len(features_df),
                'feature_count': len(features_df.columns)
            }
            
            logger.info(f"🎉 Entraînement terminé: {successful_models}/70 modèles")
            logger.info(f"📊 AUC moyenne: {summary['average_auc']:.3f} ± {summary['auc_std']:.3f}")
            logger.info(f"🎯 Précision moyenne: {summary['average_accuracy']:.3f} ± {summary['accuracy_std']:.3f}")
            
            return summary
        else:
            raise RuntimeError("Aucun modèle n'a pu être entraîné avec succès")
    
    def predict_numbers(self, data: pd.DataFrame, target_index: int, 
                       num_predictions: int = 7, method: str = 'probability') -> List[int]:
        """Prédit les numéros avec l'ensemble amélioré."""
        if not self.is_trained:
            if not self.load_models():
                raise ValueError("Aucun modèle entraîné disponible")
        
        # Créer les features pour la prédiction
        features = self.feature_engineer.create_historical_features(data, target_index)
        features_df = pd.DataFrame([features])
        
        # Obtenir les probabilités pour tous les numéros
        probabilities = {}
        for num in range(1, 71):
            if num in self.ensemble_models:
                try:
                    model = self.ensemble_models[num]
                    if hasattr(model, 'predict_proba'):
                        proba = model.predict_proba(features_df)[0, 1]
                    else:
                        proba = model.predict(features_df)[0]
                    probabilities[num] = float(proba)
                except Exception as e:
                    logger.warning(f"Erreur prédiction numéro {num}: {e}")
                    probabilities[num] = 0.0
            else:
                probabilities[num] = 0.0
        
        # Sélectionner les numéros selon la méthode
        if method == 'probability':
            # Sélection basée sur les probabilités les plus élevées
            sorted_nums = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)
            selected = [num for num, _ in sorted_nums[:num_predictions]]
        
        elif method == 'threshold':
            # Sélection basée sur un seuil adaptatif
            prob_values = list(probabilities.values())
            threshold = np.percentile(prob_values, 90 - (num_predictions * 5))
            candidates = [num for num, prob in probabilities.items() if prob >= threshold]
            
            if len(candidates) >= num_predictions:
                # Prendre les meilleurs parmi les candidats
                candidates_sorted = sorted([(num, probabilities[num]) for num in candidates], 
                                         key=lambda x: x[1], reverse=True)
                selected = [num for num, _ in candidates_sorted[:num_predictions]]
            else:
                # Compléter avec les meilleurs restants
                remaining = [num for num in range(1, 71) if num not in candidates]
                remaining_sorted = sorted([(num, probabilities[num]) for num in remaining], 
                                        key=lambda x: x[1], reverse=True)
                additional_needed = num_predictions - len(candidates)
                additional = [num for num, _ in remaining_sorted[:additional_needed]]
                selected = candidates + additional
        
        else:
            raise ValueError(f"Méthode inconnue: {method}")
        
        return sorted(selected[:num_predictions])
    
    def save_models(self):
        """Sauvegarde les modèles et métriques."""
        try:
            # Sauvegarder les modèles
            with open(self.models_file, 'wb') as f:
                pickle.dump(self.ensemble_models, f)
            
            # Sauvegarder les métriques
            with open(self.metrics_file, 'wb') as f:
                pickle.dump(self.training_metrics, f)
            
            # Sauvegarder l'importance des features
            if self.feature_importance:
                with open(self.importance_file, 'wb') as f:
                    pickle.dump(self.feature_importance, f)
            
            logger.info("✅ Modèles améliorés sauvegardés")
            
        except Exception as e:
            logger.error(f"Erreur sauvegarde modèles: {e}")
    
    def load_models(self) -> bool:
        """Charge les modèles sauvegardés."""
        try:
            if not self.models_file.exists():
                return False
            
            with open(self.models_file, 'rb') as f:
                self.ensemble_models = pickle.load(f)
            
            if self.metrics_file.exists():
                with open(self.metrics_file, 'rb') as f:
                    self.training_metrics = pickle.load(f)
            
            if self.importance_file.exists():
                with open(self.importance_file, 'rb') as f:
                    self.feature_importance = pickle.load(f)
            
            self.is_trained = len(self.ensemble_models) > 0
            logger.info(f"✅ Modèles améliorés chargés: {len(self.ensemble_models)} ensembles")
            return True
            
        except Exception as e:
            logger.error(f"Erreur chargement modèles: {e}")
            return False


if __name__ == "__main__":
    # Test du modèle amélioré
    print("🧪 Test du modèle ML amélioré...")
    
    # Créer des données de test
    test_data = []
    np.random.seed(42)
    for i in range(500):
        numbers = sorted(np.random.choice(range(1, 71), size=20, replace=False))
        test_data.append({
            'numbers': numbers,
            'date': pd.Timestamp('2023-01-01') + pd.Timedelta(days=i)
        })
    
    test_df = pd.DataFrame(test_data)
    
    # Tester le modèle
    model = EnhancedKenoMLModel()
    
    print("Entraînement du modèle amélioré...")
    try:
        metrics = model.train_models(test_df, max_samples=200)
        print("✅ Entraînement réussi!")
        print(f"📊 Modèles entraînés: {metrics['models_trained']}/70")
        print(f"🎯 AUC moyenne: {metrics['average_auc']:.3f}")
        print(f"📈 Précision moyenne: {metrics['average_accuracy']:.3f}")
        
        # Test de prédiction
        predictions = model.predict_numbers(test_df, len(test_df)-1, 7)
        print(f"🔮 Prédiction test: {predictions}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
