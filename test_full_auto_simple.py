#!/usr/bin/env python3
"""
Test simple du système Full Auto.
"""

import sys
import os

# Ajouter le répertoire actuel au path
sys.path.insert(0, os.path.dirname(__file__))

def test_full_auto():
    """Test rapide du Full Auto."""
    print("Test Full Auto...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        # Créer prédicteur
        analyzer = KenoDataAnalyzer()
        try:
            analyzer.load_all_data()  # Charger données réelles si possible
            print("  Donnees reelles chargees")
        except:
            print("  Utilisation donnees par defaut")
            
        predictor = KenoPredictor(analyzer)
        predictor.initialize_weights()
        
        print("  Predicteur cree")
        
        # Test Full Auto
        print("  Execution Full Auto...")
        predictions, details = predictor.predict_full_auto(7, 2)
        
        print(f"  Predictions: {len(predictions)} grilles")
        for i, pred in enumerate(predictions, 1):
            print(f"    Grille {i}: {sorted(pred)}")
        
        print(f"  Methode: {details['selected_method']}")
        print(f"  Roulements: {details['total_rolls']}")
        
        # Vérifications
        if details['full_auto'] and details['total_rolls'] >= 2:
            print("  OK Full Auto fonctionne!")
            return True
        else:
            print("  ERREUR Full Auto defaillant")
            return False
            
    except Exception as e:
        print(f"  ERREUR: {e}")
        return False

if __name__ == "__main__":
    success = test_full_auto()
    print("=" * 40)
    if success:
        print("SUCCES: Full Auto operationnel!")
    else:
        print("ECHEC: Probleme detecte")
    sys.exit(0 if success else 1)