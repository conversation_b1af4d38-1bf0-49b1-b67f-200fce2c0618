"""
Interface utilisateur simplifiée pour le système de prédiction Keno optimisé.
Interface claire et intuitive pour utiliser toutes les fonctionnalités.
"""

import os
import sys
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
import json

# Ajouter le répertoire courant au path pour les imports
sys.path.append(str(Path(__file__).parent))

from optimized_keno_predictor import OptimizedKenoPredictor
from keno_validator import KenoValidator

class KenoInterface:
    """Interface utilisateur pour le système de prédiction Keno."""
    
    def __init__(self):
        """Initialise l'interface."""
        self.predictor = None
        self.validator = None
        self.current_session = {
            'predictions_made': 0,
            'start_time': datetime.now(),
            'results': []
        }
        
        print("🎯 SYSTÈME DE PRÉDICTION KENO OPTIMISÉ")
        print("=" * 50)
        print("Version stable et précise pour prédire les futurs tirages")
        print("=" * 50)
    
    def run(self):
        """Lance l'interface principale."""
        try:
            self._initialize_system()
            self._main_menu()
        except KeyboardInterrupt:
            print("\n\n👋 Au revoir !")
        except Exception as e:
            print(f"\n❌ Erreur critique: {e}")
            print("Veuillez redémarrer le programme.")
    
    def _initialize_system(self):
        """Initialise le système de prédiction."""
        print("\n🔧 Initialisation du système...")
        
        # Demander le fichier de données (optionnel)
        data_file = input("\n📁 Fichier de données (optionnel, Entrée pour données de démo): ").strip()
        
        if data_file and not Path(data_file).exists():
            print(f"⚠️ Fichier non trouvé: {data_file}")
            data_file = None
        
        # Initialiser le prédicteur
        self.predictor = OptimizedKenoPredictor(data_file)
        self.validator = KenoValidator(self.predictor)
        
        # Afficher les informations sur les données
        data_info = len(self.predictor.historical_data)
        print(f"✅ Système initialisé avec {data_info} tirages historiques")
        
        if data_info < 50:
            print("⚠️ Données limitées - précision réduite")
        elif data_info < 100:
            print("📊 Données suffisantes - bonne précision")
        else:
            print("🎯 Données excellentes - précision maximale")
    
    def _main_menu(self):
        """Affiche le menu principal."""
        while True:
            print(f"\n{'='*50}")
            print("🎯 MENU PRINCIPAL")
            print(f"{'='*50}")
            print("1. 🔮 Faire une prédiction")
            print("2. 📊 Valider une prédiction")
            print("3. 🧪 Tester le système")
            print("4. 📈 Voir les performances")
            print("5. ⚙️ Configuration avancée")
            print("6. 💾 Sauvegarder/Charger")
            print("7. ❓ Aide")
            print("0. 🚪 Quitter")
            
            choice = input("\n👉 Votre choix: ").strip()
            
            if choice == "1":
                self._prediction_menu()
            elif choice == "2":
                self._validation_menu()
            elif choice == "3":
                self._testing_menu()
            elif choice == "4":
                self._performance_menu()
            elif choice == "5":
                self._advanced_menu()
            elif choice == "6":
                self._save_load_menu()
            elif choice == "7":
                self._help_menu()
            elif choice == "0":
                break
            else:
                print("❌ Choix invalide, veuillez réessayer.")
    
    def _prediction_menu(self):
        """Menu de prédiction."""
        print(f"\n{'='*40}")
        print("🔮 PRÉDICTION KENO")
        print(f"{'='*40}")
        
        # Demander le nombre de numéros
        while True:
            try:
                num_predictions = int(input("📊 Nombre de numéros à prédire (5-15): "))
                if 5 <= num_predictions <= 15:
                    break
                else:
                    print("❌ Veuillez entrer un nombre entre 5 et 15")
            except ValueError:
                print("❌ Veuillez entrer un nombre valide")
        
        # Demander la méthode
        print("\n🎯 Méthodes disponibles:")
        methods = {
            '1': ('ensemble', 'Ensemble (Recommandé) - Combine toutes les méthodes'),
            '2': ('frequency', 'Analyse de fréquence - Numéros les plus fréquents'),
            '3': ('pattern', 'Détection de patterns - Séquences et répétitions'),
            '4': ('trend', 'Analyse de tendance - Évolution des fréquences'),
            '5': ('statistical', 'Modèle statistique - Probabilités théoriques'),
            '6': ('momentum', 'Analyse momentum - Accélération des tendances')
        }
        
        for key, (method, description) in methods.items():
            print(f"{key}. {description}")
        
        method_choice = input("\n👉 Choisir la méthode (1-6, défaut=1): ").strip() or "1"
        method_name = methods.get(method_choice, methods['1'])[0]
        
        # Faire la prédiction
        print(f"\n🔄 Prédiction en cours avec la méthode '{method_name}'...")
        
        try:
            result = self.predictor.predict_next_draw(num_predictions, method_name)
            
            # Afficher les résultats
            print(f"\n{'='*50}")
            print("🎯 RÉSULTAT DE LA PRÉDICTION")
            print(f"{'='*50}")
            print(f"🔮 Numéros prédits: {result['predictions']}")
            print(f"📊 Confiance: {result['confidence']:.1%}")
            print(f"⚙️ Méthode: {result['method']}")
            print(f"🕒 Timestamp: {result['timestamp']}")
            print(f"📈 Données utilisées: {result['data_points_used']} tirages")
            
            # Afficher les détails d'analyse
            details = result.get('analysis_details', {})
            if details:
                print(f"\n📋 DÉTAILS DE L'ANALYSE:")
                freq_analysis = details.get('frequency_analysis', {})
                if freq_analysis:
                    most_freq = freq_analysis.get('most_frequent', [])[:5]
                    print(f"🔥 Numéros les plus fréquents: {most_freq}")
                
                pred_stats = details.get('prediction_stats', {})
                if pred_stats:
                    print(f"📊 Étendue des prédictions: {pred_stats.get('range', 0)}")
                    print(f"📈 Distribution: {pred_stats.get('distribution', 'N/A')}")
            
            # Enregistrer dans la session
            self.current_session['predictions_made'] += 1
            self.current_session['results'].append({
                'timestamp': result['timestamp'],
                'predictions': result['predictions'],
                'confidence': result['confidence'],
                'method': result['method']
            })
            
            # Proposer de sauvegarder
            save_choice = input(f"\n💾 Sauvegarder cette prédiction ? (o/N): ").strip().lower()
            if save_choice == 'o':
                self._save_prediction(result)
            
        except Exception as e:
            print(f"❌ Erreur lors de la prédiction: {e}")
    
    def _validation_menu(self):
        """Menu de validation."""
        print(f"\n{'='*40}")
        print("📊 VALIDATION DE PRÉDICTION")
        print(f"{'='*40}")
        
        # Demander les numéros prédits
        print("Entrez les numéros que vous aviez prédits:")
        predicted_input = input("🔮 Numéros prédits (séparés par des virgules): ").strip()
        
        try:
            predicted_numbers = [int(x.strip()) for x in predicted_input.split(',')]
            predicted_numbers = [n for n in predicted_numbers if 1 <= n <= 70]
            
            if not predicted_numbers:
                print("❌ Aucun numéro valide saisi")
                return
            
        except ValueError:
            print("❌ Format invalide. Utilisez des nombres séparés par des virgules")
            return
        
        # Demander les numéros réels
        print("\nEntrez les numéros qui sont réellement sortis:")
        actual_input = input("🎯 Numéros réels (séparés par des virgules): ").strip()
        
        try:
            actual_numbers = [int(x.strip()) for x in actual_input.split(',')]
            actual_numbers = [n for n in actual_numbers if 1 <= n <= 70]
            
            if not actual_numbers:
                print("❌ Aucun numéro valide saisi")
                return
            
        except ValueError:
            print("❌ Format invalide. Utilisez des nombres séparés par des virgules")
            return
        
        # Effectuer la validation
        validation_result = self.predictor.validate_predictions(actual_numbers, predicted_numbers)
        
        # Afficher les résultats
        print(f"\n{'='*50}")
        print("📊 RÉSULTAT DE LA VALIDATION")
        print(f"{'='*50}")
        print(f"🎯 Numéros prédits: {predicted_numbers}")
        print(f"🎲 Numéros réels: {actual_numbers}")
        print(f"✅ Numéros corrects: {validation_result['matched_numbers']}")
        print(f"📊 Réussis: {validation_result['hits']}/{len(predicted_numbers)}")
        print(f"🎯 Taux de réussite: {validation_result['hit_rate']:.1%}")
        print(f"📈 Précision: {validation_result['precision']:.1%}")
        print(f"📉 Rappel: {validation_result['recall']:.1%}")
        print(f"🔢 Score F1: {validation_result['f1_score']:.3f}")
        
        if validation_result['missed_numbers']:
            print(f"❌ Numéros manqués: {validation_result['missed_numbers']}")
        
        if validation_result['false_positives']:
            print(f"⚠️ Faux positifs: {validation_result['false_positives']}")
    
    def _testing_menu(self):
        """Menu de test du système."""
        print(f"\n{'='*40}")
        print("🧪 TEST DU SYSTÈME")
        print(f"{'='*40}")
        print("1. 🔍 Test rapide")
        print("2. 📊 Validation complète")
        print("3. 🎯 Test de précision")
        print("0. ↩️ Retour")
        
        choice = input("\n👉 Votre choix: ").strip()
        
        if choice == "1":
            self._quick_test()
        elif choice == "2":
            self._comprehensive_validation()
        elif choice == "3":
            self._accuracy_test()
    
    def _quick_test(self):
        """Test rapide du système."""
        print("\n🔄 Test rapide en cours...")
        
        try:
            # Tester plusieurs méthodes
            methods = ['ensemble', 'frequency', 'pattern']
            results = {}
            
            for method in methods:
                result = self.predictor.predict_next_draw(7, method)
                results[method] = {
                    'predictions': result['predictions'],
                    'confidence': result['confidence']
                }
            
            print(f"\n✅ Test rapide terminé:")
            for method, result in results.items():
                print(f"  {method}: {result['predictions']} (confiance: {result['confidence']:.1%})")
            
        except Exception as e:
            print(f"❌ Erreur lors du test: {e}")
    
    def _comprehensive_validation(self):
        """Validation complète du système."""
        print("\n🔄 Validation complète en cours (peut prendre quelques minutes)...")
        
        try:
            validation_results = self.validator.run_comprehensive_validation()
            
            print(f"\n{'='*50}")
            print("📊 RÉSULTATS DE VALIDATION COMPLÈTE")
            print(f"{'='*50}")
            
            # Qualité des données
            data_quality = validation_results.get('data_quality_check', {})
            print(f"📈 Qualité des données: {data_quality.get('quality_rating', 'N/A')}")
            print(f"📊 Tirages disponibles: {data_quality.get('total_draws', 0)}")
            
            # Meilleure méthode
            method_comparison = validation_results.get('method_comparison', {})
            best_method = method_comparison.get('best_method')
            if best_method:
                print(f"🏆 Meilleure méthode: {best_method}")
            
            # Résultats de backtest
            backtest_results = validation_results.get('backtest_results', {})
            if backtest_results and not backtest_results.get('error'):
                print(f"📈 Backtest: {len(backtest_results)} périodes testées")
                
                # Afficher les performances moyennes
                avg_hit_rates = []
                for period_key, results in backtest_results.items():
                    if isinstance(results, dict) and 'average_hit_rate' in results:
                        avg_hit_rates.append(results['average_hit_rate'])
                        print(f"  {period_key}: {results['average_hit_rate']:.1%} de précision")
                
                if avg_hit_rates:
                    overall_avg = sum(avg_hit_rates) / len(avg_hit_rates)
                    print(f"🎯 Précision moyenne globale: {overall_avg:.1%}")
            
            # Recommandations
            recommendations = validation_results.get('recommendations', [])
            if recommendations:
                print(f"\n💡 RECOMMANDATIONS:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"  {i}. {rec}")
            
        except Exception as e:
            print(f"❌ Erreur lors de la validation: {e}")
    
    def _accuracy_test(self):
        """Test de précision avec données simulées."""
        print("\n🎯 Test de précision en cours...")
        
        try:
            # Simuler des tests de précision
            total_tests = 20
            successful_predictions = 0
            
            print(f"Exécution de {total_tests} tests de prédiction...")
            
            for i in range(total_tests):
                if i % 5 == 0:
                    print(f"  Test {i+1}/{total_tests}...")
                
                # Faire une prédiction
                result = self.predictor.predict_next_draw(7, 'ensemble')
                
                # Simuler un résultat (pour la démonstration)
                # Dans un vrai test, on utiliserait des données réelles
                import random
                simulated_actual = sorted(random.sample(range(1, 71), 20))
                
                # Calculer les hits
                hits = len(set(result['predictions']) & set(simulated_actual))
                if hits >= 2:  # Considérer comme succès si 2+ numéros corrects
                    successful_predictions += 1
            
            accuracy = successful_predictions / total_tests
            print(f"\n📊 Résultats du test de précision:")
            print(f"  Tests réussis: {successful_predictions}/{total_tests}")
            print(f"  Taux de succès: {accuracy:.1%}")
            
            if accuracy > 0.6:
                print("🎉 Excellente précision!")
            elif accuracy > 0.4:
                print("✅ Bonne précision")
            elif accuracy > 0.2:
                print("⚠️ Précision modérée")
            else:
                print("❌ Précision faible - optimisation nécessaire")
            
        except Exception as e:
            print(f"❌ Erreur lors du test de précision: {e}")
    
    def _performance_menu(self):
        """Menu des performances."""
        print(f"\n{'='*40}")
        print("📈 PERFORMANCES DU SYSTÈME")
        print(f"{'='*40}")
        
        try:
            performance = self.predictor.get_performance_summary()
            
            if 'message' in performance:
                print(f"📊 {performance['message']}")
                return
            
            print(f"📊 Prédictions totales: {performance.get('total_predictions', 0)}")
            print(f"🎯 Précision moyenne: {performance.get('average_accuracy', 0):.1%}")
            print(f"🏆 Meilleure précision: {performance.get('best_accuracy', 0):.1%}")
            print(f"📉 Pire précision: {performance.get('worst_accuracy', 0):.1%}")
            print(f"📈 Tendance: {performance.get('accuracy_trend', 'N/A')}")
            print(f"🔒 Confiance moyenne: {performance.get('average_confidence', 0):.1%}")
            
            data_quality = performance.get('data_quality', {})
            print(f"\n📋 Qualité des données:")
            print(f"  Tirages historiques: {data_quality.get('total_historical_draws', 0)}")
            print(f"  Complétude: {data_quality.get('data_completeness', 'N/A')}")
            
            # Performances de la session actuelle
            print(f"\n🕒 Session actuelle:")
            print(f"  Prédictions faites: {self.current_session['predictions_made']}")
            duration = datetime.now() - self.current_session['start_time']
            print(f"  Durée: {duration.seconds // 60} minutes")
            
        except Exception as e:
            print(f"❌ Erreur lors de l'affichage des performances: {e}")
    
    def _advanced_menu(self):
        """Menu de configuration avancée."""
        print(f"\n{'='*40}")
        print("⚙️ CONFIGURATION AVANCÉE")
        print(f"{'='*40}")
        print("1. 🔧 Modifier les paramètres")
        print("2. 📊 Voir la configuration actuelle")
        print("3. 🔄 Réinitialiser la configuration")
        print("0. ↩️ Retour")
        
        choice = input("\n👉 Votre choix: ").strip()
        
        if choice == "1":
            self._modify_config()
        elif choice == "2":
            self._show_config()
        elif choice == "3":
            self._reset_config()
    
    def _modify_config(self):
        """Modifier la configuration."""
        print("\n🔧 Modification des paramètres:")
        print("(Appuyez sur Entrée pour garder la valeur actuelle)")
        
        config = self.predictor.config
        
        # Fenêtre d'analyse
        current_window = config['lookback_window']
        new_window = input(f"📊 Fenêtre d'analyse ({current_window}): ").strip()
        if new_window.isdigit():
            config['lookback_window'] = int(new_window)
        
        # Seuil de confiance
        current_threshold = config['prediction_confidence_threshold']
        new_threshold = input(f"🎯 Seuil de confiance ({current_threshold}): ").strip()
        try:
            if new_threshold:
                threshold_val = float(new_threshold)
                if 0 <= threshold_val <= 1:
                    config['prediction_confidence_threshold'] = threshold_val
        except ValueError:
            pass
        
        print("✅ Configuration mise à jour")
    
    def _show_config(self):
        """Afficher la configuration actuelle."""
        print(f"\n📊 Configuration actuelle:")
        config = self.predictor.config
        
        for key, value in config.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for sub_key, sub_value in value.items():
                    print(f"    {sub_key}: {sub_value}")
            else:
                print(f"  {key}: {value}")
    
    def _reset_config(self):
        """Réinitialiser la configuration."""
        confirm = input("⚠️ Réinitialiser la configuration ? (o/N): ").strip().lower()
        if confirm == 'o':
            # Réinitialiser avec les valeurs par défaut
            self.predictor.config = {
                'lookback_window': 100,
                'min_data_points': 50,
                'prediction_confidence_threshold': 0.6,
                'ensemble_weights': {
                    'frequency_analysis': 0.25,
                    'pattern_detection': 0.25,
                    'trend_analysis': 0.20,
                    'statistical_model': 0.15,
                    'momentum_analysis': 0.15
                }
            }
            print("✅ Configuration réinitialisée")
    
    def _save_load_menu(self):
        """Menu de sauvegarde/chargement."""
        print(f"\n{'='*40}")
        print("💾 SAUVEGARDE/CHARGEMENT")
        print(f"{'='*40}")
        print("1. 💾 Sauvegarder l'état")
        print("2. 📂 Charger un état")
        print("3. 📋 Exporter les résultats")
        print("0. ↩️ Retour")
        
        choice = input("\n👉 Votre choix: ").strip()
        
        if choice == "1":
            filename = input("📁 Nom du fichier (optionnel): ").strip()
            self.predictor.save_state(filename if filename else None)
        elif choice == "2":
            filename = input("📁 Nom du fichier: ").strip()
            if filename and Path(filename).exists():
                self.predictor.load_state(filename)
            else:
                print("❌ Fichier non trouvé")
        elif choice == "3":
            self._export_results()
    
    def _export_results(self):
        """Exporter les résultats de la session."""
        if not self.current_session['results']:
            print("❌ Aucun résultat à exporter")
            return
        
        filename = f"keno_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.current_session, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"✅ Résultats exportés: {filename}")
            
        except Exception as e:
            print(f"❌ Erreur lors de l'export: {e}")
    
    def _save_prediction(self, prediction_result):
        """Sauvegarder une prédiction."""
        filename = f"prediction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(prediction_result, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"✅ Prédiction sauvegardée: {filename}")
            
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde: {e}")
    
    def _help_menu(self):
        """Menu d'aide."""
        print(f"\n{'='*50}")
        print("❓ AIDE - SYSTÈME DE PRÉDICTION KENO")
        print(f"{'='*50}")
        print("""
🎯 UTILISATION BASIQUE:
  1. Faire une prédiction avec le menu principal
  2. Choisir le nombre de numéros (5-15 recommandé)
  3. Sélectionner la méthode (Ensemble recommandé)
  4. Noter les numéros prédits et la confiance

📊 MÉTHODES DE PRÉDICTION:
  • Ensemble: Combine toutes les méthodes (RECOMMANDÉ)
  • Fréquence: Analyse les numéros les plus fréquents
  • Pattern: Détecte les séquences et répétitions
  • Tendance: Analyse l'évolution des fréquences
  • Statistique: Utilise les probabilités théoriques
  • Momentum: Analyse l'accélération des tendances

🎯 CONSEILS POUR MAXIMISER LA PRÉCISION:
  • Utilisez la méthode "Ensemble" pour les meilleures performances
  • Plus vous avez de données historiques, meilleure est la précision
  • Surveillez le score de confiance (>60% recommandé)
  • Validez régulièrement vos prédictions pour améliorer le système
  • Effectuez des tests périodiques pour vérifier les performances

📈 INTERPRÉTATION DES RÉSULTATS:
  • Confiance >80%: Très fiable
  • Confiance 60-80%: Fiable
  • Confiance 40-60%: Modérément fiable
  • Confiance <40%: Peu fiable

⚠️ AVERTISSEMENT:
  Ce système est basé sur l'analyse statistique et ne garantit pas
  de gains. Utilisez-le de manière responsable.
        """)
        
        input("\nAppuyez sur Entrée pour continuer...")


if __name__ == "__main__":
    # Lancer l'interface
    interface = KenoInterface()
    interface.run()
