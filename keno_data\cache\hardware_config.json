{"hardware_info": {"cpu": {"name": "Intel64 Family 6 Model 198 Stepping 2, GenuineIntel", "cores_physical": 24, "cores_logical": 24, "frequency_max": 2700.0, "frequency_current": 2009.0, "architecture": "AMD64", "features": [], "supports_avx": true, "supports_avx2": true, "supports_sse": true}, "memory": {"total_gb": 31.671024322509766, "available_gb": 25.5191650390625, "used_percent": 19.4, "swap_total_gb": 33.5308723449707, "swap_used_percent": 2.5}, "gpu": {"nvidia": [{"name": "NVIDIA GeForce RTX 5080 Laptop GPU", "memory_total_gb": 15.9208984375, "detected_via": "nvidia-smi"}], "amd": [], "intel": [], "available": true}, "npu": {"available": false, "devices": []}, "storage": {"total_gb": 926.0546836853027, "free_gb": 362.65185546875, "used_percent": 60.83904526830422, "type": "Unknown (Windows)"}, "platform": {"system": "Windows", "release": "11", "version": "10.0.26100", "architecture": ["64bit", "WindowsPE"], "python_version": "3.12.7"}}, "optimization_config": {"threading": {"max_workers": 22, "chunk_size": "large", "parallel_predictions": true, "parallel_backtesting": true, "threading_mode": "aggressive"}, "memory": {"max_memory_usage_gb": 8, "cache_strategy": "balanced", "preload_data": true, "batch_size": "medium"}, "compute": {"use_gpu": true, "use_npu": false, "precision": "mixed", "optimization_level": "high", "gpu_backend": "nvidia", "gpu_memory_growth": true, "mixed_precision": true}, "storage": {"io_strategy": "balanced", "cache_to_disk": true, "temp_file_size_limit_gb": 2, "compression": true}, "ml_acceleration": {"catboost_task_type": "GPU", "catboost_thread_count": 4, "sklearn_n_jobs": 4, "numpy_threads": 8, "catboost_devices": "0", "high_performance_mode": true, "parallel_feature_engineering": true}}, "detection_timestamp": "2025-08-16T14:53:04.739911"}