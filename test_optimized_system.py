"""
Test complet du système de prédiction Keno optimisé.
Vérifie que toutes les fonctionnalités marchent sans bug.
"""

import sys
import traceback
from pathlib import Path
from datetime import datetime

def test_optimized_predictor():
    """Test du prédicteur optimisé."""
    print("🧪 Test du prédicteur optimisé...")
    
    try:
        from optimized_keno_predictor import OptimizedKenoPredictor
        
        # Test d'initialisation
        predictor = OptimizedKenoPredictor()
        print(f"✅ Prédicteur initialisé avec {len(predictor.historical_data)} tirages")
        
        # Test des différentes méthodes
        methods = ['ensemble', 'frequency', 'pattern', 'trend', 'statistical', 'momentum']
        
        for method in methods:
            try:
                result = predictor.predict_next_draw(7, method)
                print(f"✅ Méthode {method}: {result['predictions']} (confiance: {result['confidence']:.1%})")
            except Exception as e:
                print(f"❌ Erreur méthode {method}: {e}")
                return False
        
        # Test de validation
        test_actual = [1, 5, 12, 23, 34, 45, 56, 67]
        test_predicted = result['predictions']
        
        validation = predictor.validate_predictions(test_actual, test_predicted)
        print(f"✅ Validation: {validation['hits']} hits sur {len(test_predicted)}")
        
        # Test des performances
        performance = predictor.get_performance_summary()
        print(f"✅ Performances: {performance.get('total_predictions', 0)} prédictions totales")
        
        # Test de sauvegarde/chargement
        predictor.save_state()
        print("✅ Sauvegarde réussie")
        
        predictor.load_state()
        print("✅ Chargement réussi")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test prédicteur: {e}")
        traceback.print_exc()
        return False

def test_validator():
    """Test du validateur."""
    print("\n🧪 Test du validateur...")
    
    try:
        from optimized_keno_predictor import OptimizedKenoPredictor
        from keno_validator import KenoValidator
        
        predictor = OptimizedKenoPredictor()
        validator = KenoValidator(predictor)
        
        print("✅ Validateur initialisé")
        
        # Test de vérification de qualité des données
        data_quality = validator._check_data_quality()
        print(f"✅ Qualité des données: {data_quality.get('quality_rating', 'N/A')}")
        
        # Test de comparaison des méthodes (version simplifiée)
        print("🔄 Test comparaison des méthodes...")
        method_comparison = validator._compare_prediction_methods()
        
        if 'error' not in method_comparison:
            best_method = method_comparison.get('best_method')
            print(f"✅ Meilleure méthode identifiée: {best_method}")
        else:
            print(f"⚠️ Comparaison limitée: {method_comparison['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test validateur: {e}")
        traceback.print_exc()
        return False

def test_interface():
    """Test de l'interface (import seulement)."""
    print("\n🧪 Test de l'interface...")
    
    try:
        from keno_interface import KenoInterface
        
        # Test d'initialisation seulement (pas d'exécution interactive)
        print("✅ Interface importée avec succès")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test interface: {e}")
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """Exécute tous les tests du système optimisé."""
    print("🚀 TESTS COMPLETS DU SYSTÈME KENO OPTIMISÉ")
    print("=" * 60)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("Prédicteur Optimisé", test_optimized_predictor),
        ("Validateur", test_validator),
        ("Interface Utilisateur", test_interface)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 Test: {test_name}")
        print("-" * 40)
        
        try:
            success = test_func()
            results[test_name] = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        except Exception as e:
            results[test_name] = f"❌ ERREUR: {e}"
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in results.items():
        print(f"{result} {test_name}")
        if "✅" in result:
            success_count += 1
    
    print(f"\n🎯 Résultat global: {success_count}/{len(tests)} tests réussis")
    
    if success_count == len(tests):
        print("\n🎉 TOUS LES TESTS SONT PASSÉS!")
        print("✅ Le système optimisé est opérationnel et sans bug")
        print("\n🚀 PRÊT POUR UTILISATION:")
        print("  • Lancez 'python keno_interface.py' pour l'interface complète")
        print("  • Ou utilisez directement 'OptimizedKenoPredictor' dans votre code")
        
        # Démonstration rapide
        print(f"\n{'='*50}")
        print("🔮 DÉMONSTRATION RAPIDE")
        print(f"{'='*50}")
        
        try:
            from optimized_keno_predictor import OptimizedKenoPredictor
            
            predictor = OptimizedKenoPredictor()
            result = predictor.predict_next_draw(7, 'ensemble')
            
            print(f"🎯 Prédiction exemple: {result['predictions']}")
            print(f"📊 Confiance: {result['confidence']:.1%}")
            print(f"⚙️ Méthode: {result['method']}")
            print(f"📈 Données utilisées: {result['data_points_used']} tirages")
            
            print(f"\n💡 CONSEILS D'UTILISATION:")
            print("  • Utilisez la méthode 'ensemble' pour la meilleure précision")
            print("  • Surveillez le score de confiance (>60% recommandé)")
            print("  • Validez vos prédictions pour améliorer le système")
            print("  • Plus de données historiques = meilleure précision")
            
        except Exception as e:
            print(f"⚠️ Erreur démonstration: {e}")
        
    elif success_count >= len(tests) * 0.7:
        print("\n⚠️ SYSTÈME PARTIELLEMENT OPÉRATIONNEL")
        print("Certains composants nécessitent des ajustements")
    else:
        print("\n🚨 SYSTÈME NÉCESSITE DES CORRECTIONS")
        print("Plusieurs composants ont des problèmes")
    
    print(f"\n📁 Fichiers créés:")
    files = [
        "optimized_keno_predictor.py - Prédicteur principal optimisé",
        "keno_validator.py - Système de validation robuste", 
        "keno_interface.py - Interface utilisateur complète",
        "test_optimized_system.py - Tests de validation"
    ]
    
    for file_desc in files:
        print(f"  • {file_desc}")
    
    return results

if __name__ == "__main__":
    try:
        results = run_comprehensive_test()
        
        # Sauvegarder les résultats
        import json
        results_file = Path("test_results_optimized.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': results,
                'system_status': 'operational' if all('✅' in r for r in results.values()) else 'partial'
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 Résultats sauvegardés: {results_file}")
        
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n💥 Erreur critique: {e}")
        traceback.print_exc()
        sys.exit(1)
