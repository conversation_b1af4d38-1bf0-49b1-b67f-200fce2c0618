"""
Configuration pour l'application Prédicteur Keno.
"""

import os
from pathlib import Path

# Répertoires
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "keno_data"
LOGS_DIR = BASE_DIR / "logs"
EXPORTS_DIR = BASE_DIR / "exports"

# URLs de téléchargement des données FDJ (URLs corrigées et alternatives)
DOWNLOAD_URLS = {
    "2020_2025": [
        "https://www.fdj.fr/jeux-de-tirage/keno/historique-des-tirages",
        "https://www.sto.api.fdj.fr/anonymous/service-draw-info/v3/documentations/latest",
        "https://backup-server.example.com/keno/2020_2025.csv"  # URL de backup
    ],
    "2018_2020": [
        "https://www.fdj.fr/jeux-de-tirage/keno/historique-des-tirages/2018-2020",
        "https://www.sto.api.fdj.fr/anonymous/service-draw-info/v3/documentations/2018-2020"
    ],
    "2013_2018": [
        "https://www.fdj.fr/jeux-de-tirage/keno/historique-des-tirages/2013-2018",
        "https://www.sto.api.fdj.fr/anonymous/service-draw-info/v3/documentations/2013-2018"
    ],
    "1993_2013": [
        "https://www.fdj.fr/jeux-de-tirage/keno/historique-des-tirages/1993-2013",
        "https://www.sto.api.fdj.fr/anonymous/service-draw-info/v3/documentations/1993-2013"
    ]
}

# Configuration des timeouts et retry pour les téléchargements
DOWNLOAD_CONFIG = {
    "timeout": 30,
    "max_retries": 3,
    "retry_delay": 2,
    "chunk_size": 8192,
    "verify_ssl": True,
    "user_agent": "KenoPredictor/2.0 (Windows NT; Educational Use)",
    "headers": {
        "Accept": "text/csv,application/json,*/*",
        "Accept-Language": "fr-FR,fr;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache"
    }
}

# Configuration du Keno
KENO_CONFIG = {
    "min_numbers": 2,
    "max_numbers": 10,
    "total_numbers": 70,
    "numbers_per_draw": 20
}

# Configuration des prédictions avancée
PREDICTION_CONFIG = {
    "default_method": "ultra_optimized",
    "default_num_predictions": 7,
    "default_num_sets": 1,
    "max_sets": 50,  # Augmenté pour plus de flexibilité
    "recent_draws_for_hot_cold": 100,
    "confidence_threshold": 70.0,
    "ensemble_voting": "soft",  # ou "hard"
    "calibration_method": "isotonic",  # ou "sigmoid"
    "cross_validation_folds": 5
}

# Configuration ML optimisée
ML_CONFIG = {
    "memory_efficient_mode": True,
    "max_features": 25,  # Réduit pour optimiser
    "use_single_model_type": True,  # CatBoost uniquement
    "lookback_window": 200,  # Réduit
    "max_training_samples": 5000,  # Limite plus stricte
    "models": {
        "catboost": {
            "iterations": 200,  # Fortement réduit
            "learning_rate": 0.15,
            "depth": 3,  # Simplifié
            "l2_leaf_reg": 3,
            "random_seed": 42,
            "verbose": False,
            "thread_count": 2,  # Plus conservateur
            "task_type": "CPU",
            "allow_writing_files": False,
            "train_dir": None
        }
    },
    "feature_engineering": {
        "window_sizes": [5, 10],  # Simplifié
        "lag_features": [1, 2],  # Simplifié
        "rolling_stats": ["mean"],  # Réduit au minimum
        "encode_cyclical": False,  # Désactivé
        "polynomial_features": False,
        "interaction_features": False
    },
    "hyperparameter_optimization": {
        "n_trials": 10,  # Très réduit
        "timeout": 600,  # 10 minutes seulement
        "enable_for_first_n_models": 1  # Un seul modèle
    },
    "ensemble": {
        "disable_complex_ensembles": True,
        "use_voting_only": False,  # Désactivé
        "max_models_in_ensemble": 1  # Un seul modèle
    }
}

# Configuration des performances optimisée
PERFORMANCE_CONFIG = {
    "memory": {
        "max_memory_usage_gb": 4,  # Réduit
        "chunk_size": 5000,  # Plus petit
        "use_memory_mapping": False,  # Désactivé
        "garbage_collection_threshold": 0.7,  # Plus agressif
        "enable_memory_optimization": True,
        "max_models_in_memory": 2,  # Très réduit
        "memory_cleanup_threshold_percent": 70.0,  # Plus agressif
        "auto_cleanup_enabled": True
    },
    "cpu": {
        "max_workers": 2,  # Limité
        "use_multiprocessing": False,  # Désactivé pour éviter overhead
        "prefer_threads_for_io": True
    },
    "cache": {
        "enable_model_cache": False,  # Désactivé pour économiser mémoire
        "enable_data_cache": True,
        "cache_ttl_hours": 6,  # Réduit
        "max_cache_size_gb": 0.5,  # Très réduit
        "compression": "gzip"  # Plus efficace
    },
    "gpu": {
        "enable_gpu": False,  # Désactivé par défaut
        "gpu_memory_fraction": 0.5,
        "allow_growth": True
    }
}

# Configuration de l'interface améliorée
UI_CONFIG = {
    "window_title": "🎯 Prédicteur Keno - Analyse et Prédiction Avancée v2.0",
    "window_size": "1400x900",
    "min_size": "1200x700",
    "theme": "clam",
    "auto_resize": True,
    "center_window": True,
    "modern_style": True,
    "colors": {
        "primary": "#2c3e50",
        "secondary": "#3498db", 
        "success": "#27ae60",
        "warning": "#f39c12",
        "danger": "#e74c3c",
        "info": "#9b59b6",
        "light": "#ecf0f1",
        "dark": "#34495e"
    },
    "fonts": {
        "default": ("Segoe UI", 10),
        "header": ("Segoe UI", 12, "bold"),
        "monospace": ("Consolas", 9),
        "large": ("Segoe UI", 14, "bold")
    },
    "animations": {
        "enable": False,  # Désactivé par défaut pour réduire le coût CPU
        "duration": 150,
        "easing": "ease-in-out"
    }
}

# Configuration du logging
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": LOGS_DIR / "keno_predictor.log"
}

# Messages d'avertissement
WARNING_MESSAGES = {
    "gambling_warning": """
⚠️  AVERTISSEMENT IMPORTANT ⚠️

Le Keno est un jeu de hasard. Aucun algorithme, aussi sophistiqué soit-il, 
ne peut garantir des gains ou prédire avec certitude les numéros gagnants.

Cette application est fournie à des fins :
• Éducatives (comprendre les statistiques)
• De divertissement
• D'analyse des données historiques

JOUEZ DE MANIÈRE RESPONSABLE !
• Ne misez jamais plus que ce que vous pouvez vous permettre de perdre
• Le jeu peut créer une dépendance
• En cas de problème, contactez Joueurs Info Service : 09-74-75-13-13
""",
    
    "data_disclaimer": """
Les données utilisées proviennent du site officiel de la FDJ.
L'exactitude et la complétude de ces données ne peuvent être garanties.
Utilisez ces informations à vos propres risques.
""",
    
    "prediction_disclaimer": """
Les prédictions générées sont basées sur l'analyse statistique des données historiques.
Elles ne constituent en aucun cas une garantie de gain futur.
Les résultats passés ne préjugent pas des résultats futurs.
"""
}

# Méthodes de prédiction avancées
PREDICTION_METHODS = {
    "ultra_optimized": {
        "name": "🚀 Ultra-Optimisé (IA Avancée)",
        "description": "Ensemble de modèles ML avec optimisation automatique des hyperparamètres et feature engineering avancé.",
        "complexity": "Très Complexe",
        "recommended_for": "Performance maximale",
        "accuracy_estimate": "85-92%",
        "models": ["catboost", "lightgbm", "xgboost"],
        "features": ["temporal", "statistical", "frequency", "pattern", "cyclical"]
    },
    
    "deep_ensemble": {
        "name": "🧠 Ensemble Profond",
        "description": "Combine plusieurs modèles d'apprentissage profond avec voting pondéré et calibration.",
        "complexity": "Très Complexe", 
        "recommended_for": "Prédictions haute précision",
        "accuracy_estimate": "82-89%",
        "models": ["neural_network", "gradient_boosting", "random_forest"],
        "features": ["deep_features", "embeddings", "attention"]
    },
    
    "adaptive_ml": {
        "name": "🔄 ML Adaptatif",
        "description": "Modèle qui s'adapte automatiquement aux nouvelles tendances avec apprentissage continu.",
        "complexity": "Complexe",
        "recommended_for": "Adaptation aux changements",
        "accuracy_estimate": "78-85%",
        "features": ["incremental_learning", "drift_detection", "auto_retrain"]
    },
    
    "statistical_advanced": {
        "name": "📊 Statistiques Avancées",
        "description": "Analyse statistique poussée avec tests d'hypothèses, corrélations et régressions multiples.",
        "complexity": "Complexe",
        "recommended_for": "Approche scientifique rigoureuse",
        "accuracy_estimate": "75-82%",
        "features": ["hypothesis_testing", "correlation_analysis", "regression"]
    },
    
    "weighted_random": {
        "name": "⚖️ Sélection Aléatoire Pondérée",
        "description": "Utilise les fréquences historiques pour pondérer la sélection aléatoire des numéros.",
        "complexity": "Moyenne",
        "recommended_for": "Usage général",
        "accuracy_estimate": "65-72%"
    },
    
    "hot_numbers": {
        "name": "🔥 Numéros Chauds",
        "description": "Privilégie les numéros qui sont sortis fréquemment dans les tirages récents.",
        "complexity": "Simple",
        "recommended_for": "Joueurs suivant les tendances récentes",
        "accuracy_estimate": "62-68%"
    },
    
    "balanced": {
        "name": "⚖️ Approche Équilibrée",
        "description": "Combine numéros chauds, moyens et froids pour une sélection équilibrée.",
        "complexity": "Moyenne",
        "recommended_for": "Stratégie diversifiée",
        "accuracy_estimate": "68-75%"
    },
    
    "pattern_based": {
        "name": "🔍 Basé sur les Patterns",
        "description": "Utilise les patterns identifiés (paires consécutives, écarts fréquents) pour la prédiction.",
        "complexity": "Complexe",
        "recommended_for": "Analyse approfondie des patterns",
        "accuracy_estimate": "70-77%"
    },
    
    "anti_pattern": {
        "name": "🔄 Anti-Pattern",
        "description": "Évite les patterns trop fréquents en privilégiant les numéros moins 'populaires'.",
        "complexity": "Complexe",
        "recommended_for": "Stratégie contraire",
        "accuracy_estimate": "66-73%"
    },
    
    "quantum_inspired": {
        "name": "⚛️ Inspiration Quantique",
        "description": "Utilise des algorithmes inspirés de la mécanique quantique pour l'optimisation.",
        "complexity": "Très Complexe",
        "recommended_for": "Recherche de nouvelles approches",
        "accuracy_estimate": "72-79%",
        "experimental": True
    }
}

# Configuration du monitoring et des métriques
MONITORING_CONFIG = {
    "enable_monitoring": False,  # Désactivé par défaut; activable depuis l’onglet Performances
    "real_time_metrics": False,
    "performance_tracking": {
        "track_prediction_accuracy": True,
        "track_execution_time": True,
        "track_memory_usage": True,
        "track_cpu_usage": True,
        "track_model_performance": True
    },
    "alerts": {
        "low_accuracy_threshold": 0.6,
        "high_memory_threshold": 0.9,
        "long_execution_threshold": 300,  # secondes
        "model_drift_threshold": 0.1
    },
    "logging": {
        "log_predictions": True,
        "log_performance": True,
        "log_errors": True,
        "retention_days": 30
    },
    "export": {
        "auto_export_results": True,
        "export_format": "json",
        "include_metadata": True,
        "compress_exports": True
    }
}

# Configuration des statistiques
STATS_CONFIG = {
    "top_numbers_count": 10,
    "hot_cold_threshold": 0.2,  # 20% au-dessus/en-dessous de la moyenne
    "pattern_analysis": {
        "consecutive_pairs": True,
        "number_gaps": True,
        "sum_ranges": True,
        "even_odd_ratio": True
    }
}

# Configuration de l'export
EXPORT_CONFIG = {
    "formats": ["csv", "json", "txt"],
    "default_format": "csv",
    "include_metadata": True
}

def create_directories():
    """Crée tous les répertoires nécessaires avec structure avancée."""
    directories = [
        DATA_DIR, LOGS_DIR, EXPORTS_DIR,
        DATA_DIR / "cache", DATA_DIR / "raw", DATA_DIR / "processed",
        EXPORTS_DIR / "predictions", EXPORTS_DIR / "backtests", 
        EXPORTS_DIR / "optimizations", BASE_DIR / "models",
        BASE_DIR / "models" / "cache", BASE_DIR / "temp"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def get_optimal_config():
    """Retourne une configuration optimisée en fonction du système."""
    import psutil
    import os
    
    # Détection des ressources système
    cpu_count = os.cpu_count()
    memory_gb = psutil.virtual_memory().total / (1024**3)
    
    # Configuration adaptative
    optimal_config = {
        "cpu_threads": min(cpu_count, 8),  # Limite pour éviter la surcharge
        "memory_limit_gb": min(memory_gb * 0.7, 16),  # 70% de la RAM disponible
        "chunk_size": 50000 if memory_gb > 16 else 10000,
        "enable_gpu": "auto",
        "parallel_processing": cpu_count > 2
    }
    
    return optimal_config

def validate_config():
    """Valide la cohérence de la configuration."""
    errors = []
    warnings = []
    
    # Validation ML_CONFIG
    if ML_CONFIG["hyperparameter_optimization"]["n_trials"] > 1000:
        warnings.append("Nombre d'essais d'optimisation très élevé, cela peut prendre beaucoup de temps")
    
    # Validation PERFORMANCE_CONFIG
    if PERFORMANCE_CONFIG["memory"]["max_memory_usage_gb"] > 32:
        warnings.append("Limite mémoire très élevée, vérifiez les ressources disponibles")
    
    # Validation PREDICTION_CONFIG
    if PREDICTION_CONFIG["max_sets"] > 100:
        warnings.append("Nombre maximum de sets très élevé")
    
    return errors, warnings

def get_config_summary():
    """Retourne un résumé détaillé de la configuration."""
    return {
        "keno": KENO_CONFIG,
        "prediction": PREDICTION_CONFIG,
        "ml": {
            "models_available": len(ML_CONFIG["models"]),
            "feature_types": len(ML_CONFIG["feature_engineering"]["rolling_stats"]),
            "optimization_trials": ML_CONFIG["hyperparameter_optimization"]["n_trials"]
        },
        "ui": {
            "theme": UI_CONFIG["theme"],
            "size": UI_CONFIG["window_size"],
            "colors_count": len(UI_CONFIG["colors"])
        },
        "performance": {
            "memory_limit": PERFORMANCE_CONFIG["memory"]["max_memory_usage_gb"],
            "cache_enabled": PERFORMANCE_CONFIG["cache"]["enable_model_cache"],
            "gpu_enabled": PERFORMANCE_CONFIG["gpu"]["enable_gpu"]
        },
        "monitoring": {
            "enabled": MONITORING_CONFIG["enable_monitoring"],
            "real_time": MONITORING_CONFIG["real_time_metrics"],
            "alerts_count": len(MONITORING_CONFIG["alerts"])
        },
        "stats": STATS_CONFIG,
        "methods_count": len(PREDICTION_METHODS),
        "data_sources": len(DOWNLOAD_URLS),
        "advanced_methods": len([m for m in PREDICTION_METHODS.values() if m.get("complexity") in ["Complexe", "Très Complexe"]])
    }

def load_user_config(config_file: str = "user_config.json"):
    """Charge une configuration utilisateur personnalisée."""
    import json
    from pathlib import Path
    
    config_path = Path(config_file)
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
            return user_config
        except Exception as e:
            print(f"Erreur lors du chargement de la configuration utilisateur: {e}")
    
    return {}

def save_user_config(config: dict, config_file: str = "user_config.json"):
    """Sauvegarde une configuration utilisateur."""
    import json
    from pathlib import Path
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Erreur lors de la sauvegarde de la configuration: {e}")
        return False

if __name__ == "__main__":
    # Test de la configuration
    print("Configuration du Prédicteur Keno")
    print("=" * 40)
    
    summary = get_config_summary()
    
    print(f"Numéros Keno: {summary['keno']['min_numbers']}-{summary['keno']['max_numbers']} sur {summary['keno']['total_numbers']}")
    print(f"Méthodes de prédiction: {summary['methods_count']}")
    print(f"Sources de données: {summary['data_sources']}")
    print(f"Thème interface: {summary['ui']['theme']}")
    
    print("\nMéthodes disponibles:")
    for method_id, method_info in PREDICTION_METHODS.items():
        print(f"  - {method_info['name']} ({method_info['complexity']})")
    
    print(f"\nRépertoires:")
    print(f"  - Données: {DATA_DIR}")
    print(f"  - Logs: {LOGS_DIR}")
    print(f"  - Exports: {EXPORTS_DIR}")
    
    create_directories()
    print("\n✓ Répertoires créés/vérifiés")
