"""
Système de prédiction adaptatif avancé pour Keno.
S'adapte automatiquement aux patterns changeants et optimise les performances en temps réel.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import pickle
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from collections import deque, defaultdict
import threading
import time

# Imports conditionnels
try:
    from enhanced_ml_model import EnhancedKenoMLModel
    ENHANCED_ML_AVAILABLE = True
except ImportError:
    ENHANCED_ML_AVAILABLE = False

try:
    from ml_model import KenoMLModel
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

from data_analyzer import KenoDataAnalyzer
from keno_predictor import KenoPredictor

logger = logging.getLogger(__name__)

class AdaptiveKenoPredictor:
    """Système de prédiction adaptatif qui s'améliore automatiquement."""
    
    def __init__(self, cache_dir="keno_data/cache"):
        """Initialise le prédicteur adaptatif."""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Composants principaux
        self.analyzer = KenoDataAnalyzer(cache_dir)
        self.base_predictor = None  # Sera initialisé à la demande pour éviter la récursion
        
        # Modèles ML améliorés
        self.enhanced_ml = None
        if ENHANCED_ML_AVAILABLE:
            self.enhanced_ml = EnhancedKenoMLModel(cache_dir)
        elif ML_AVAILABLE:
            self.enhanced_ml = KenoMLModel(cache_dir)
        
        # Système adaptatif
        self.adaptation_history = deque(maxlen=1000)  # Historique des adaptations
        self.performance_tracker = defaultdict(list)  # Suivi des performances par méthode
        self.pattern_detector = PatternDetector()
        self.strategy_optimizer = StrategyOptimizer()
        
        # Configuration adaptative
        self.adaptation_config = {
            'min_samples_for_adaptation': 50,
            'performance_window': 100,
            'adaptation_threshold': 0.05,
            'retraining_interval_hours': 24,
            'pattern_sensitivity': 0.1,
            'auto_optimization': True
        }
        
        # État du système
        self.last_adaptation = None
        self.last_retraining = None
        self.current_best_strategy = None
        self.adaptation_lock = threading.Lock()
        
        # Fichiers de persistance
        self.adaptation_file = self.cache_dir / "adaptive_state.pkl"
        self.performance_file = self.cache_dir / "adaptive_performance.json"
        
        # Charger l'état précédent
        self.load_adaptive_state()
    
    def predict_adaptive(self, num_predictions: int = 7, 
                        context: Optional[Dict] = None) -> Dict:
        """Prédiction adaptative avec optimisation automatique."""
        logger.info("🧠 Prédiction adaptative en cours...")
        
        # Analyser le contexte actuel
        current_context = self._analyze_current_context(context)
        
        # Détecter les patterns récents
        recent_patterns = self.pattern_detector.detect_recent_patterns(
            self.analyzer.data, window_size=50
        )
        
        # Sélectionner la stratégie optimale
        optimal_strategy = self._select_optimal_strategy(current_context, recent_patterns)
        
        # Générer les prédictions avec la stratégie sélectionnée
        predictions = self._generate_predictions_with_strategy(
            optimal_strategy, num_predictions
        )
        
        # Calculer la confiance adaptative
        confidence = self._calculate_adaptive_confidence(
            predictions, optimal_strategy, recent_patterns
        )
        
        # Enregistrer pour l'adaptation future
        prediction_record = {
            'timestamp': datetime.now(),
            'predictions': predictions,
            'strategy': optimal_strategy,
            'context': current_context,
            'patterns': recent_patterns,
            'confidence': confidence
        }
        
        self.adaptation_history.append(prediction_record)
        
        # Déclencher l'adaptation si nécessaire
        if self._should_adapt():
            threading.Thread(target=self._perform_adaptation, daemon=True).start()
        
        return {
            'predictions': predictions,
            'strategy': optimal_strategy,
            'confidence': confidence,
            'context': current_context,
            'patterns_detected': len(recent_patterns),
            'adaptation_status': 'active' if self.last_adaptation else 'pending'
        }
    
    def _analyze_current_context(self, context: Optional[Dict]) -> Dict:
        """Analyse le contexte actuel pour l'adaptation."""
        current_context = {
            'timestamp': datetime.now(),
            'data_freshness': self._calculate_data_freshness(),
            'recent_volatility': self._calculate_recent_volatility(),
            'pattern_stability': self._calculate_pattern_stability(),
            'performance_trend': self._calculate_performance_trend()
        }
        
        if context:
            current_context.update(context)
        
        return current_context
    
    def _select_optimal_strategy(self, context: Dict, patterns: List[Dict]) -> Dict:
        """Sélectionne la stratégie optimale basée sur le contexte et les patterns."""
        # Évaluer les stratégies disponibles
        available_strategies = self._get_available_strategies()
        strategy_scores = {}
        
        for strategy_name, strategy_config in available_strategies.items():
            score = self._evaluate_strategy_for_context(
                strategy_name, strategy_config, context, patterns
            )
            strategy_scores[strategy_name] = score
        
        # Sélectionner la meilleure stratégie
        best_strategy_name = max(strategy_scores, key=strategy_scores.get)
        best_strategy = available_strategies[best_strategy_name]
        best_strategy['name'] = best_strategy_name
        best_strategy['score'] = strategy_scores[best_strategy_name]
        
        logger.info(f"🎯 Stratégie sélectionnée: {best_strategy_name} (score: {best_strategy['score']:.3f})")
        
        return best_strategy
    
    def _get_available_strategies(self) -> Dict:
        """Retourne les stratégies disponibles avec leur configuration."""
        strategies = {
            'enhanced_ml': {
                'type': 'ml_ensemble',
                'weight': 0.4,
                'requires_training': True,
                'adaptation_speed': 'slow',
                'pattern_sensitivity': 'high'
            },
            'adaptive_hybrid': {
                'type': 'hybrid',
                'weight': 0.3,
                'ml_ratio': 0.6,
                'statistical_ratio': 0.4,
                'adaptation_speed': 'medium'
            },
            'pattern_focused': {
                'type': 'statistical',
                'weight': 0.2,
                'focus': 'recent_patterns',
                'adaptation_speed': 'fast',
                'pattern_sensitivity': 'very_high'
            },
            'momentum_based': {
                'type': 'statistical',
                'weight': 0.1,
                'focus': 'momentum',
                'lookback_window': 20,
                'adaptation_speed': 'fast'
            }
        }
        
        # Ajuster selon la disponibilité des modèles
        if not self.enhanced_ml or not self.enhanced_ml.is_trained:
            strategies['enhanced_ml']['weight'] = 0.0
            # Redistribuer les poids
            total_weight = sum(s['weight'] for s in strategies.values())
            if total_weight > 0:
                for strategy in strategies.values():
                    if strategy['weight'] > 0:
                        strategy['weight'] /= total_weight
        
        return strategies
    
    def _evaluate_strategy_for_context(self, strategy_name: str, strategy_config: Dict,
                                     context: Dict, patterns: List[Dict]) -> float:
        """Évalue une stratégie pour le contexte actuel."""
        base_score = strategy_config.get('weight', 0.1)
        
        # Ajustements basés sur le contexte
        adjustments = 0.0
        
        # Performance historique
        if strategy_name in self.performance_tracker:
            recent_performance = self.performance_tracker[strategy_name][-10:]
            if recent_performance:
                avg_performance = np.mean(recent_performance)
                adjustments += (avg_performance - 0.5) * 0.3
        
        # Adaptation à la volatilité
        volatility = context.get('recent_volatility', 0.5)
        if strategy_config.get('adaptation_speed') == 'fast' and volatility > 0.7:
            adjustments += 0.2
        elif strategy_config.get('adaptation_speed') == 'slow' and volatility < 0.3:
            adjustments += 0.1
        
        # Sensibilité aux patterns
        pattern_strength = len(patterns) / 10.0  # Normaliser
        if strategy_config.get('pattern_sensitivity') == 'high' and pattern_strength > 0.5:
            adjustments += 0.15
        
        # Fraîcheur des données
        data_freshness = context.get('data_freshness', 0.5)
        if strategy_config.get('requires_training') and data_freshness < 0.3:
            adjustments -= 0.2
        
        final_score = max(0.0, min(1.0, base_score + adjustments))
        return final_score
    
    def _generate_predictions_with_strategy(self, strategy: Dict, num_predictions: int) -> List[int]:
        """Génère les prédictions avec la stratégie sélectionnée."""
        strategy_type = strategy.get('type', 'statistical')
        strategy_name = strategy.get('name', 'unknown')
        
        try:
            if strategy_type == 'ml_ensemble' and self.enhanced_ml and self.enhanced_ml.is_trained:
                # Utiliser le modèle ML amélioré
                predictions = self.enhanced_ml.predict_numbers(
                    self.analyzer.data, 
                    len(self.analyzer.data) - 1, 
                    num_predictions,
                    method='probability'
                )
                
            elif strategy_type == 'hybrid':
                # Stratégie hybride ML + statistique
                ml_ratio = strategy.get('ml_ratio', 0.5)
                ml_count = int(num_predictions * ml_ratio)
                stat_count = num_predictions - ml_count
                
                ml_predictions = []
                if self.enhanced_ml and self.enhanced_ml.is_trained and ml_count > 0:
                    ml_predictions = self.enhanced_ml.predict_numbers(
                        self.analyzer.data, len(self.analyzer.data) - 1, ml_count
                    )
                
                # Initialiser le prédicteur de base si nécessaire
                if self.base_predictor is None:
                    # Import local pour éviter la récursion
                    from keno_predictor import KenoPredictor
                    self.base_predictor = KenoPredictor(self.analyzer)
                    # Désactiver les composants avancés pour éviter la récursion
                    self.base_predictor.enhanced_ml = None
                    self.base_predictor.adaptive_predictor = None

                stat_predictions = self.base_predictor.predict(
                    stat_count, method='pattern_based'
                )
                
                # Combiner en évitant les doublons
                combined = list(ml_predictions)
                for num in stat_predictions:
                    if num not in combined and len(combined) < num_predictions:
                        combined.append(num)
                
                # Compléter si nécessaire
                if len(combined) < num_predictions:
                    remaining = [n for n in range(1, 71) if n not in combined]
                    additional = np.random.choice(remaining, 
                                                num_predictions - len(combined), 
                                                replace=False)
                    combined.extend(additional)
                
                predictions = sorted(combined[:num_predictions])
                
            else:
                # Stratégies statistiques
                focus = strategy.get('focus', 'balanced')
                if focus == 'recent_patterns':
                    method = 'pattern_based'
                elif focus == 'momentum':
                    method = 'hot_numbers'
                else:
                    method = 'balanced'
                
                predictions = self.base_predictor.predict(num_predictions, method=method)
            
            logger.info(f"✅ Prédictions générées avec {strategy_name}: {predictions}")
            return predictions
            
        except Exception as e:
            logger.error(f"Erreur génération prédictions avec {strategy_name}: {e}")
            # Fallback vers méthode de base
            return self.base_predictor.predict(num_predictions, method='balanced')
    
    def _calculate_adaptive_confidence(self, predictions: List[int], 
                                     strategy: Dict, patterns: List[Dict]) -> float:
        """Calcule la confiance adaptative pour les prédictions."""
        base_confidence = 0.5
        
        # Confiance basée sur la stratégie
        strategy_confidence = strategy.get('score', 0.5)
        
        # Confiance basée sur les patterns détectés
        pattern_confidence = min(1.0, len(patterns) / 5.0) * 0.2
        
        # Confiance basée sur la performance historique récente
        strategy_name = strategy.get('name', 'unknown')
        historical_confidence = 0.0
        if strategy_name in self.performance_tracker:
            recent_perf = self.performance_tracker[strategy_name][-5:]
            if recent_perf:
                historical_confidence = np.mean(recent_perf) * 0.3
        
        # Confiance basée sur la cohérence des prédictions
        consistency_confidence = self._calculate_prediction_consistency(predictions) * 0.1
        
        total_confidence = (base_confidence + strategy_confidence + 
                          pattern_confidence + historical_confidence + 
                          consistency_confidence)
        
        return min(1.0, max(0.0, total_confidence))
    
    def _should_adapt(self) -> bool:
        """Détermine si une adaptation est nécessaire."""
        if not self.adaptation_config['auto_optimization']:
            return False
        
        # Vérifier le nombre d'échantillons
        if len(self.adaptation_history) < self.adaptation_config['min_samples_for_adaptation']:
            return False
        
        # Vérifier l'intervalle depuis la dernière adaptation
        if self.last_adaptation:
            time_since_adaptation = datetime.now() - self.last_adaptation
            if time_since_adaptation.total_seconds() < 3600:  # Minimum 1 heure
                return False
        
        # Vérifier les changements de performance
        recent_performance = self._calculate_recent_performance_change()
        if abs(recent_performance) > self.adaptation_config['adaptation_threshold']:
            return True
        
        # Vérifier les changements de patterns
        pattern_change = self._calculate_pattern_change()
        if pattern_change > self.adaptation_config['pattern_sensitivity']:
            return True
        
        return False
    
    def _perform_adaptation(self):
        """Effectue l'adaptation du système."""
        with self.adaptation_lock:
            logger.info("🔄 Adaptation du système en cours...")
            
            try:
                # Analyser les performances récentes
                performance_analysis = self._analyze_recent_performance()
                
                # Optimiser les stratégies
                self.strategy_optimizer.optimize_strategies(
                    self.adaptation_history, performance_analysis
                )
                
                # Réentraîner les modèles si nécessaire
                if self._should_retrain():
                    self._retrain_models()
                
                # Mettre à jour la configuration
                self._update_adaptation_config(performance_analysis)
                
                self.last_adaptation = datetime.now()
                self.save_adaptive_state()
                
                logger.info("✅ Adaptation terminée avec succès")
                
            except Exception as e:
                logger.error(f"Erreur lors de l'adaptation: {e}")
    
    def _calculate_data_freshness(self) -> float:
        """Calcule la fraîcheur des données."""
        if self.analyzer.data is None or len(self.analyzer.data) == 0:
            return 0.0
        
        try:
            last_date = pd.to_datetime(self.analyzer.data.iloc[-1].get('date', '1900-01-01'))
            days_old = (datetime.now() - last_date).days
            freshness = max(0.0, 1.0 - (days_old / 30.0))  # Fraîcheur sur 30 jours
            return freshness
        except:
            return 0.5
    
    def _calculate_recent_volatility(self) -> float:
        """Calcule la volatilité récente des tirages."""
        if self.analyzer.data is None or len(self.analyzer.data) < 10:
            return 0.5
        
        try:
            recent_data = self.analyzer.data.tail(20)
            number_frequencies = defaultdict(int)
            
            for _, row in recent_data.iterrows():
                numbers = row.get('numbers', [])
                for num in numbers:
                    number_frequencies[num] += 1
            
            frequencies = list(number_frequencies.values())
            if len(frequencies) > 1:
                volatility = np.std(frequencies) / np.mean(frequencies)
                return min(1.0, volatility)
            
            return 0.5
        except:
            return 0.5
    
    def _calculate_pattern_stability(self) -> float:
        """Calcule la stabilité des patterns récents."""
        try:
            recent_patterns = self.pattern_detector.detect_recent_patterns(
                self.analyzer.data, window_size=30
            )
            older_patterns = self.pattern_detector.detect_recent_patterns(
                self.analyzer.data, window_size=60, offset=30
            )
            
            # Comparer la similarité des patterns
            similarity = self._calculate_pattern_similarity(recent_patterns, older_patterns)
            return similarity
        except:
            return 0.5
    
    def _calculate_performance_trend(self) -> float:
        """Calcule la tendance de performance récente."""
        if len(self.performance_tracker) == 0:
            return 0.5
        
        try:
            all_recent_scores = []
            for strategy_scores in self.performance_tracker.values():
                all_recent_scores.extend(strategy_scores[-10:])
            
            if len(all_recent_scores) > 5:
                # Calculer la tendance (régression linéaire simple)
                x = np.arange(len(all_recent_scores))
                y = np.array(all_recent_scores)
                slope = np.polyfit(x, y, 1)[0]
                
                # Normaliser la tendance
                trend = 0.5 + slope * 2  # Ajuster l'échelle
                return max(0.0, min(1.0, trend))
            
            return 0.5
        except:
            return 0.5
    
    def save_adaptive_state(self):
        """Sauvegarde l'état adaptatif."""
        try:
            state = {
                'last_adaptation': self.last_adaptation,
                'last_retraining': self.last_retraining,
                'current_best_strategy': self.current_best_strategy,
                'adaptation_config': self.adaptation_config,
                'adaptation_history': list(self.adaptation_history)[-100:]  # Garder les 100 derniers
            }
            
            with open(self.adaptation_file, 'wb') as f:
                pickle.dump(state, f)
            
            # Sauvegarder les performances en JSON pour lisibilité
            performance_data = {
                strategy: scores[-50:] for strategy, scores in self.performance_tracker.items()
            }
            
            with open(self.performance_file, 'w') as f:
                json.dump(performance_data, f, indent=2, default=str)
            
            logger.info("État adaptatif sauvegardé")
            
        except Exception as e:
            logger.error(f"Erreur sauvegarde état adaptatif: {e}")
    
    def load_adaptive_state(self):
        """Charge l'état adaptatif précédent."""
        try:
            if self.adaptation_file.exists():
                with open(self.adaptation_file, 'rb') as f:
                    state = pickle.load(f)
                
                self.last_adaptation = state.get('last_adaptation')
                self.last_retraining = state.get('last_retraining')
                self.current_best_strategy = state.get('current_best_strategy')
                self.adaptation_config.update(state.get('adaptation_config', {}))
                
                # Charger l'historique
                history = state.get('adaptation_history', [])
                self.adaptation_history.extend(history)
            
            if self.performance_file.exists():
                with open(self.performance_file, 'r') as f:
                    performance_data = json.load(f)
                
                for strategy, scores in performance_data.items():
                    self.performance_tracker[strategy] = scores
            
            logger.info("État adaptatif chargé")
            
        except Exception as e:
            logger.error(f"Erreur chargement état adaptatif: {e}")


class PatternDetector:
    """Détecteur de patterns avancé pour l'adaptation."""
    
    def detect_recent_patterns(self, data: pd.DataFrame, window_size: int = 50,
                             offset: int = 0) -> List[Dict]:
        """Détecte les patterns dans les données récentes."""
        patterns = []

        if data is None or len(data) < window_size + offset:
            return patterns
        
        # Sélectionner la fenêtre de données
        start_idx = max(0, len(data) - window_size - offset)
        end_idx = len(data) - offset if offset > 0 else len(data)
        window_data = data.iloc[start_idx:end_idx]
        
        # Détecter différents types de patterns
        patterns.extend(self._detect_frequency_patterns(window_data))
        patterns.extend(self._detect_sequence_patterns(window_data))
        patterns.extend(self._detect_gap_patterns(window_data))
        
        return patterns
    
    def _detect_frequency_patterns(self, data: pd.DataFrame) -> List[Dict]:
        """Détecte les patterns de fréquence."""
        patterns = []
        
        # Compter les fréquences
        number_counts = defaultdict(int)
        for _, row in data.iterrows():
            numbers = row.get('numbers', [])
            for num in numbers:
                number_counts[num] += 1
        
        if number_counts:
            frequencies = list(number_counts.values())
            mean_freq = np.mean(frequencies)
            std_freq = np.std(frequencies)
            
            # Identifier les numéros très fréquents ou très rares
            hot_numbers = [num for num, count in number_counts.items() 
                          if count > mean_freq + std_freq]
            cold_numbers = [num for num, count in number_counts.items() 
                           if count < mean_freq - std_freq]
            
            if hot_numbers:
                patterns.append({
                    'type': 'hot_numbers',
                    'numbers': hot_numbers,
                    'strength': len(hot_numbers) / 70.0,
                    'confidence': min(1.0, std_freq / mean_freq)
                })
            
            if cold_numbers:
                patterns.append({
                    'type': 'cold_numbers',
                    'numbers': cold_numbers,
                    'strength': len(cold_numbers) / 70.0,
                    'confidence': min(1.0, std_freq / mean_freq)
                })
        
        return patterns
    
    def _detect_sequence_patterns(self, data: pd.DataFrame) -> List[Dict]:
        """Détecte les patterns de séquence."""
        patterns = []
        
        # Analyser les séquences consécutives
        consecutive_sequences = []
        
        for _, row in data.iterrows():
            numbers = sorted(row.get('numbers', []))
            sequences = []
            current_seq = [numbers[0]] if numbers else []
            
            for i in range(1, len(numbers)):
                if numbers[i] == numbers[i-1] + 1:
                    current_seq.append(numbers[i])
                else:
                    if len(current_seq) >= 3:
                        sequences.append(current_seq)
                    current_seq = [numbers[i]]
            
            if len(current_seq) >= 3:
                sequences.append(current_seq)
            
            consecutive_sequences.extend(sequences)
        
        if consecutive_sequences:
            avg_seq_length = np.mean([len(seq) for seq in consecutive_sequences])
            patterns.append({
                'type': 'consecutive_sequences',
                'sequences': consecutive_sequences,
                'average_length': avg_seq_length,
                'strength': len(consecutive_sequences) / len(data),
                'confidence': min(1.0, avg_seq_length / 5.0)
            })
        
        return patterns
    
    def _detect_gap_patterns(self, data: pd.DataFrame) -> List[Dict]:
        """Détecte les patterns d'écarts."""
        patterns = []
        
        # Analyser les écarts entre tirages successifs
        gaps = []
        
        for i in range(1, len(data)):
            prev_numbers = set(data.iloc[i-1].get('numbers', []))
            curr_numbers = set(data.iloc[i].get('numbers', []))
            
            # Calculer l'écart (nombres différents)
            gap = len(prev_numbers.symmetric_difference(curr_numbers))
            gaps.append(gap)
        
        if gaps:
            mean_gap = np.mean(gaps)
            std_gap = np.std(gaps)
            
            patterns.append({
                'type': 'gap_pattern',
                'mean_gap': mean_gap,
                'std_gap': std_gap,
                'stability': 1.0 - (std_gap / mean_gap) if mean_gap > 0 else 0.0,
                'strength': min(1.0, mean_gap / 20.0),
                'confidence': 1.0 - min(1.0, std_gap / 10.0)
            })
        
        return patterns


class StrategyOptimizer:
    """Optimiseur de stratégies pour l'adaptation."""
    
    def optimize_strategies(self, adaptation_history: deque, 
                          performance_analysis: Dict):
        """Optimise les stratégies basées sur l'historique."""
        logger.info("🔧 Optimisation des stratégies...")
        
        # Analyser les performances par stratégie
        strategy_performance = defaultdict(list)
        
        for record in adaptation_history:
            strategy_name = record.get('strategy', {}).get('name', 'unknown')
            confidence = record.get('confidence', 0.5)
            strategy_performance[strategy_name].append(confidence)
        
        # Identifier les stratégies les plus performantes
        best_strategies = {}
        for strategy, performances in strategy_performance.items():
            if len(performances) >= 5:  # Minimum d'échantillons
                avg_performance = np.mean(performances)
                stability = 1.0 - np.std(performances)
                score = avg_performance * 0.7 + stability * 0.3
                best_strategies[strategy] = score
        
        if best_strategies:
            best_strategy = max(best_strategies, key=best_strategies.get)
            logger.info(f"🏆 Meilleure stratégie identifiée: {best_strategy} (score: {best_strategies[best_strategy]:.3f})")
        
        return best_strategies


if __name__ == "__main__":
    # Test du prédicteur adaptatif
    print("🧠 Test du prédicteur adaptatif...")
    
    try:
        predictor = AdaptiveKenoPredictor()
        
        # Charger des données de test
        if predictor.analyzer.load_processed_data():
            print("✅ Données chargées")
            
            # Test de prédiction adaptative
            result = predictor.predict_adaptive(7)
            
            print(f"🔮 Prédictions: {result['predictions']}")
            print(f"🎯 Stratégie: {result['strategy']['name']}")
            print(f"📊 Confiance: {result['confidence']:.1%}")
            print(f"🔍 Patterns détectés: {result['patterns_detected']}")
            
        else:
            print("❌ Impossible de charger les données")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
