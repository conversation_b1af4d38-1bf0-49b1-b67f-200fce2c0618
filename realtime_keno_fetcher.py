"""
Module pour récupérer les données Keno en temps réel depuis les sources officielles FDJ.
Gère les deux tirages quotidiens : midi (13h45) et soir (21h00).
"""

import requests
import pandas as pd
import json
import zipfile
import os
from datetime import datetime, timedelta
from pathlib import Path
import logging
import time
import re
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

class RealtimeKenoFetcher:
    """Récupérateur de données Keno en temps réel."""
    
    def __init__(self, data_dir="keno_data"):
        """
        Initialise le récupérateur temps réel.
        
        Args:
            data_dir (str): Répertoire de stockage des données
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # URLs officielles mises à jour
        self.base_urls = {
            "csv_historical": "https://cdn-media.fdj.fr/static-draws/csv/keno/",
            "results_page": "https://www.fdj.fr/jeux-de-tirage/resultats",
            "keno_page": "https://www.fdj.fr/jeux-de-tirage/keno/historique",
            "api_latest": "https://www.sto.api.fdj.fr/anonymous/service-draw-info/v3/documentations/1a2b3c4d-9876-4562-b3fc-2c963f66aft6"
        }
        
        # Configuration des horaires de tirage
        self.tirage_schedule = {
            "midi": {
                "limit_participation": "13:00",
                "publication": "13:45",
                "backup_check": "14:30"
            },
            "soir": {
                "limit_participation": "20:00", 
                "publication": "21:00",
                "backup_check": "22:00"
            }
        }
        
        # Cache pour éviter les requêtes répétitives
        self.cache_dir = self.data_dir / "realtime_cache"
        self.cache_dir.mkdir(exist_ok=True)
        
    def get_current_month_csv_url(self, year=None, month=None):
        """
        Génère l'URL pour télécharger le CSV du mois en cours.
        
        Args:
            year (int): Année (défaut: année actuelle)
            month (int): Mois (défaut: mois actuel)
            
        Returns:
            str: URL du fichier CSV
        """
        if year is None:
            year = datetime.now().year
        if month is None:
            month = datetime.now().month
            
        filename = f"keno_{year}{month:02d}.zip"
        return self.base_urls["csv_historical"] + filename
        
    def download_latest_csv_data(self, months_back=1):
        """
        Télécharge les derniers fichiers CSV disponibles.
        
        Args:
            months_back (int): Nombre de mois à télécharger en arrière
            
        Returns:
            list: Liste des fichiers téléchargés
        """
        downloaded_files = []
        current_date = datetime.now()
        
        for i in range(months_back + 1):
            target_date = current_date - timedelta(days=i * 30)
            year, month = target_date.year, target_date.month
            
            url = self.get_current_month_csv_url(year, month)
            filename = f"keno_{year}{month:02d}.zip"
            filepath = self.data_dir / filename
            
            try:
                logger.info(f"Téléchargement de {filename}...")
                response = requests.get(url, timeout=30)
                
                if response.status_code == 200:
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    
                    # Extraire le ZIP
                    with zipfile.ZipFile(filepath, 'r') as zip_ref:
                        extract_dir = self.data_dir / f"keno_{year}{month:02d}"
                        extract_dir.mkdir(exist_ok=True)
                        zip_ref.extractall(extract_dir)
                    
                    downloaded_files.append(str(filepath))
                    logger.info(f"[DOWNLOAD] {filename} téléchargé et extrait")
                    
                else:
                    logger.warning(f"[DOWNLOAD] {filename} non disponible (HTTP {response.status_code})")
                    
            except Exception as e:
                logger.error(f"[DOWNLOAD] Erreur téléchargement {filename}: {e}")
                
        return downloaded_files
    
    def parse_csv_data(self, csv_file_path):
        """
        Parse un fichier CSV de données Keno avec gestion des deux tirages.
        
        Args:
            csv_file_path (str): Chemin vers le fichier CSV
            
        Returns:
            pandas.DataFrame: Données parsées avec distinction midi/soir
        """
        try:
            # Essayer différents encodages et séparateurs
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                for sep in [';', ',', '\t']:
                    try:
                        df = pd.read_csv(csv_file_path, encoding=encoding, sep=sep)
                        if len(df.columns) > 3:
                            break
                    except:
                        continue
                else:
                    continue
                break
            else:
                raise ValueError("Impossible de parser le fichier CSV")
            
            # Standardiser les colonnes
            df.columns = df.columns.str.lower().str.strip()
            
            # Identifier les tirages midi/soir basé sur l'heure
            if 'heure' in df.columns:
                df['tirage_type'] = df['heure'].apply(self._classify_tirage)
            else:
                # Fallback : utiliser l'index pour estimer
                df['tirage_type'] = ['midi' if i % 2 == 0 else 'soir' for i in range(len(df))]
            
            # Extraire les numéros gagnants
            numero_cols = [col for col in df.columns if 'n' in col and col.replace('n', '').isdigit()]
            if not numero_cols:
                numero_cols = [f'n{i}' for i in range(1, 21)]  # N1 à N20
            
            # Créer une structure standardisée
            parsed_data = []
            for _, row in df.iterrows():
                try:
                    # Extraire les numéros
                    numeros = []
                    for col in numero_cols[:20]:  # Maximum 20 numéros
                        if col in row and pd.notna(row[col]):
                            numeros.append(int(row[col]))
                    
                    if len(numeros) >= 15:  # Valider qu'on a assez de numéros
                        parsed_data.append({
                            'date': self._parse_date(row.get('date', '')),
                            'heure': row.get('heure', ''),
                            'tirage_type': row.get('tirage_type', 'unknown'),
                            'numeros': sorted(numeros),
                            'multiplicateur': row.get('multiplicateur', 1),
                            'joker_plus': row.get('joker+', ''),
                            'source': 'csv_officiel'
                        })
                        
                except Exception as e:
                    logger.warning(f"Erreur parsing ligne: {e}")
                    continue
            
            return pd.DataFrame(parsed_data)
            
        except Exception as e:
            logger.error(f"Erreur parsing CSV {csv_file_path}: {e}")
            return None
    
    def _classify_tirage(self, heure_str):
        """
        Classifie un tirage comme midi ou soir basé sur l'heure.
        
        Args:
            heure_str (str): String de l'heure
            
        Returns:
            str: 'midi' ou 'soir'
        """
        try:
            if pd.isna(heure_str):
                return 'unknown'
                
            # Extraire l'heure numérique
            heure_match = re.search(r'(\d{1,2})', str(heure_str))
            if heure_match:
                heure = int(heure_match.group(1))
                return 'midi' if heure < 17 else 'soir'
        except:
            pass
            
        return 'unknown'
    
    def _parse_date(self, date_str):
        """
        Parse une date depuis différents formats.
        
        Args:
            date_str (str): String de date
            
        Returns:
            datetime: Date parsée ou None
        """
        if pd.isna(date_str):
            return None
            
        date_formats = [
            '%d/%m/%Y', '%Y-%m-%d', '%d-%m-%Y', '%d.%m.%Y'
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(str(date_str), fmt)
            except:
                continue
                
        return None
    
    def check_missing_recent_data(self, last_n_days=3):
        """
        Vérifie si on a toutes les données des derniers jours.
        Compatible avec les données existantes et nouvelles.
        
        Args:
            last_n_days (int): Nombre de jours à vérifier
            
        Returns:
            dict: Rapport des données manquantes
        """
        missing_report = {
            'missing_dates': [],
            'missing_tirages': [],
            'total_expected': last_n_days * 2,  # 2 tirages par jour
            'total_found': 0
        }
        
        try:
            # Essayer d'abord avec l'analyzer existant (plus fiable)
            from data_analyzer import KenoDataAnalyzer
            analyzer = KenoDataAnalyzer()
            
            if analyzer.load_processed_data():
                logger.info("Utilisation des données de l'analyzer existant")
                data = analyzer.data
                
                if data is not None and not data.empty and 'date' in data.columns:
                    data_with_dates = data[data['date'].notna()].copy()
                    
                    # Vérifier les derniers jours
                    end_date = datetime.now().date()
                    start_date = end_date - timedelta(days=last_n_days)
                    
                    for i in range(last_n_days):
                        check_date = start_date + timedelta(days=i)
                        
                        # Compter les tirages pour cette date
                        day_draws = data_with_dates[data_with_dates['date'].dt.date == check_date]
                        count = len(day_draws)
                        
                        # Déterminer le nombre attendu selon l'heure actuelle
                        expected_for_day = 2
                        if check_date == datetime.now().date():
                            # Pour aujourd'hui, ajuster selon l'heure
                            now = datetime.now()
                            if now.hour < 13:
                                expected_for_day = 0
                            elif now.hour < 21:
                                expected_for_day = 1
                            else:
                                expected_for_day = 2
                        
                        # Calculer ce qui manque
                        found_for_day = min(count, expected_for_day)
                        missing_for_day = expected_for_day - found_for_day
                        
                        missing_report['total_found'] += found_for_day
                        
                        # Ajouter les tirages manquants
                        if missing_for_day > 0:
                            if expected_for_day == 2:
                                if count == 0:
                                    missing_report['missing_tirages'].extend([
                                        f"{check_date} midi", f"{check_date} soir"
                                    ])
                                elif count == 1:
                                    # Difficile de savoir lequel manque sans plus d'infos
                                    missing_report['missing_tirages'].append(f"{check_date} (1 tirage manquant)")
                            elif expected_for_day == 1 and count == 0:
                                missing_report['missing_tirages'].append(f"{check_date} midi")
                                
                            if check_date not in [datetime.strptime(d, '%Y-%m-%d').date() if isinstance(d, str) else d for d in missing_report['missing_dates']]:
                                missing_report['missing_dates'].append(str(check_date))
                    
                    return missing_report
            
            # Fallback sur l'analyse des CSV si l'analyzer n'est pas disponible
            logger.info("Fallback sur l'analyse directe des CSV")
            csv_files = list(self.data_dir.glob("*/keno_*.csv"))
            if csv_files:
                latest_csv = max(csv_files, key=os.path.getctime)
                df = self.parse_csv_data(latest_csv)
                
                if df is not None and not df.empty:
                    # Utiliser la même logique mais avec les données parsées
                    end_date = datetime.now().date()
                    start_date = end_date - timedelta(days=last_n_days)
                    
                    for i in range(last_n_days):
                        check_date = start_date + timedelta(days=i)
                        
                        # Compter les tirages pour cette date
                        day_draws = df[df['date'].dt.date == check_date] if 'date' in df.columns else []
                        count = len(day_draws)
                        
                        missing_report['total_found'] += min(count, 2)
                        
                        if count < 2:
                            missing_count = 2 - count
                            for j in range(missing_count):
                                tirage_type = "midi" if j == 0 else "soir"
                                missing_report['missing_tirages'].append(f"{check_date} {tirage_type}")
                            
                            missing_report['missing_dates'].append(str(check_date))
                            
        except Exception as e:
            logger.error(f"Erreur vérification données manquantes: {e}")
            
        return missing_report
    
    def update_data_if_needed(self):
        """
        Met à jour les données si nécessaire.
        
        Returns:
            dict: Rapport de la mise à jour
        """
        logger.info("[CHECK] Vérification des données manquantes...")
        
        # Vérifier les données manquantes
        missing_report = self.check_missing_recent_data()
        
        update_report = {
            'update_needed': len(missing_report['missing_tirages']) > 0,
            'missing_count': len(missing_report['missing_tirages']),
            'actions_taken': [],
            'success': False
        }
        
        if update_report['update_needed']:
            logger.info(f"📥 {update_report['missing_count']} tirages manquants détectés")
            
            # Priorité 1: Essayer l'API FDJ
            try:
                logger.info("[UPDATE] Tentative via API FDJ...")
                api_result = self.download_latest_from_api()
                
                if api_result.get('success'):
                    update_report['actions_taken'].append(f"API FDJ: {api_result.get('message', 'Données téléchargées')}")
                    update_report['success'] = True
                    logger.info("[UPDATE] Données mises à jour via API FDJ")
                else:
                    logger.warning(f"[UPDATE] API failed: {api_result.get('error', 'Unknown error')}")
                    
                    # Priorité 2: Fallback vers CSV traditionnel
                    logger.info("[UPDATE] Fallback vers téléchargement CSV...")
                    downloaded = self.download_latest_csv_data(months_back=1)
                    if downloaded:
                        update_report['actions_taken'].append(f"CSV fallback: {len(downloaded)} fichiers")
                        update_report['success'] = True
                        logger.info("[UPDATE] Données mises à jour via CSV fallback")
                    else:
                        logger.warning("[UPDATE] Aucun nouveau fichier téléchargé")
                        update_report['actions_taken'].append("Échec: Aucune source disponible")
                        
            except Exception as e:
                logger.error(f"[UPDATE] Erreur mise à jour: {e}")
                update_report['actions_taken'].append(f"Erreur: {e}")
        else:
            logger.info("[UPDATE] Données à jour")
            update_report['success'] = True
            
        return update_report
    
    def get_today_expected_tirages(self):
        """
        Retourne les horaires des tirages d'aujourd'hui.
        
        Returns:
            dict: Informations sur les tirages du jour
        """
        now = datetime.now()
        today = now.date()
        
        tirages_info = {
            'date': str(today),
            'midi': {
                'heure_limite': f"{today} 13:00:00",
                'heure_resultat': f"{today} 13:45:00",
                'status': 'en_attente'
            },
            'soir': {
                'heure_limite': f"{today} 20:00:00", 
                'heure_resultat': f"{today} 21:00:00",
                'status': 'en_attente'
            }
        }
        
        # Déterminer le statut basé sur l'heure actuelle
        if now.time() >= datetime.strptime("13:45", "%H:%M").time():
            tirages_info['midi']['status'] = 'resultat_disponible'
            
        if now.time() >= datetime.strptime("21:00", "%H:%M").time():
            tirages_info['soir']['status'] = 'resultat_disponible'
            
        return tirages_info
    
    def download_latest_from_api(self):
        """
        Télécharge les dernières données depuis l'API FDJ.
        
        Returns:
            dict: Résultat du téléchargement avec status et données
        """
        try:
            logger.info("[API] Téléchargement depuis l'API FDJ...")
            
            # Faire la requête vers l'API
            response = requests.get(
                self.base_urls["api_latest"],
                timeout=30,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8'
                }
            )
            
            if response.status_code == 200:
                # Traiter la réponse selon le type de contenu
                content_type = response.headers.get('content-type', '').lower()
                
                if 'application/json' in content_type:
                    # Réponse JSON - traiter directement
                    data = response.json()
                    return self._process_api_json_data(data)
                    
                elif 'application/zip' in content_type or 'application/octet-stream' in content_type:
                    # Fichier ZIP - vérifier s'il existe déjà et nettoyer les anciens
                    self._cleanup_old_api_files()
                    
                    # Utiliser un nom fixe pour éviter les duplicatas
                    zip_filename = "keno_api_latest.zip"
                    zip_path = self.data_dir / zip_filename
                    
                    with open(zip_path, 'wb') as f:
                        f.write(response.content)
                    
                    # Extraire le ZIP dans un dossier fixe
                    extract_dir = self.data_dir / "api_latest"
                    if extract_dir.exists():
                        import shutil
                        shutil.rmtree(extract_dir)
                    extract_dir.mkdir(exist_ok=True)
                    
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(extract_dir)
                    
                    # Chercher les fichiers CSV dans l'extraction
                    csv_files = list(extract_dir.glob("*.csv"))
                    if csv_files:
                        csv_data = self.parse_csv_data(csv_files[0])
                        if csv_data is not None and not csv_data.empty:
                            return {
                                'success': True,
                                'source': 'api_zip',
                                'file_path': str(csv_files[0]),
                                'files': [str(f) for f in csv_files],  # Liste des fichiers pour compatibilité
                                'data': csv_data,
                                'message': f'Données mises à jour depuis l\'API (ZIP): {len(csv_data)} tirages'
                            }
                    
                    return {
                        'success': False,
                        'error': 'Aucun fichier CSV trouvé dans l\'archive téléchargée'
                    }
                    
                else:
                    # Autre type de contenu - essayer de traiter comme texte/CSV
                    content = response.text
                    
                    # Sauvegarder temporairement comme CSV
                    temp_csv = self.cache_dir / f"temp_api_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                    with open(temp_csv, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    # Parser le CSV
                    csv_data = self.parse_csv_data(temp_csv)
                    if csv_data is not None and not csv_data.empty:
                        return {
                            'success': True,
                            'source': 'api_csv',
                            'file_path': str(temp_csv),
                            'data': csv_data,
                            'message': f'Données mises à jour depuis l\'API (CSV): {len(csv_data)} tirages'
                        }
                    
                    return {
                        'success': False,
                        'error': 'Impossible de parser les données reçues de l\'API'
                    }
                    
            else:
                return {
                    'success': False,
                    'error': f'Erreur HTTP {response.status_code} lors de l\'accès à l\'API'
                }
                
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': 'Timeout lors de la connexion à l\'API FDJ'
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': 'Erreur de connexion à l\'API FDJ'
            }
        except Exception as e:
            logger.error(f"Erreur lors du téléchargement API: {e}")
            return {
                'success': False,
                'error': f'Erreur inattendue: {str(e)}'
            }
    
    def _process_api_json_data(self, json_data):
        """
        Traite les données JSON reçues de l'API.
        
        Args:
            json_data (dict): Données JSON de l'API
            
        Returns:
            dict: Résultat du traitement
        """
        try:
            processed_data = []
            
            # Adapter selon la structure des données de l'API FDJ
            if isinstance(json_data, dict):
                if 'draws' in json_data:
                    draws = json_data['draws']
                elif 'tirages' in json_data:
                    draws = json_data['tirages']
                elif 'results' in json_data:
                    draws = json_data['results']
                else:
                    # Si la structure est différente, essayer de traiter directement
                    draws = [json_data] if not isinstance(json_data, list) else json_data
            else:
                draws = json_data if isinstance(json_data, list) else [json_data]
            
            for draw in draws:
                try:
                    # Extraire les informations de base
                    date_str = draw.get('date', draw.get('day', ''))
                    time_str = draw.get('time', draw.get('heure', ''))
                    
                    # Extraire les numéros gagnants
                    numeros = []
                    if 'numbers' in draw:
                        numeros = draw['numbers']
                    elif 'numeros' in draw:
                        numeros = draw['numeros']
                    elif 'winningNumbers' in draw:
                        numeros = draw['winningNumbers']
                    else:
                        # Chercher les numéros dans les clés N1, N2, etc.
                        for i in range(1, 21):
                            key = f'N{i}'
                            if key in draw and draw[key]:
                                numeros.append(int(draw[key]))
                    
                    if len(numeros) >= 15:  # Vérifier qu'on a assez de numéros
                        processed_data.append({
                            'date': self._parse_date(date_str),
                            'heure': time_str,
                            'tirage_type': self._classify_tirage(time_str),
                            'numeros': sorted(numeros),
                            'multiplicateur': draw.get('multiplicateur', draw.get('multiplier', 1)),
                            'joker_plus': draw.get('joker+', draw.get('jokerPlus', '')),
                            'source': 'api_json'
                        })
                        
                except Exception as e:
                    logger.warning(f"Erreur traitement d'un tirage JSON: {e}")
                    continue
            
            if processed_data:
                df = pd.DataFrame(processed_data)
                return {
                    'success': True,
                    'source': 'api_json',
                    'data': df,
                    'message': f'Données mises à jour depuis l\'API (JSON): {len(df)} tirages'
                }
            else:
                return {
                    'success': False,
                    'error': 'Aucune donnée valide trouvée dans la réponse JSON de l\'API'
                }
                
        except Exception as e:
            logger.error(f"Erreur traitement JSON API: {e}")
            return {
                'success': False,
                'error': f'Erreur lors du traitement des données JSON: {str(e)}'
            }
    
    def _cleanup_old_api_files(self):
        """
        Nettoie les anciens fichiers API pour éviter l'accumulation.
        """
        try:
            # Supprimer les anciens dossiers api_latest_*
            for old_dir in self.data_dir.glob("api_latest_*"):
                if old_dir.is_dir():
                    import shutil
                    shutil.rmtree(old_dir)
                    logger.info(f"[CLEANUP] Ancien dossier API supprimé: {old_dir.name}")
            
            # Supprimer les anciens fichiers ZIP api_latest_*
            for old_zip in self.data_dir.glob("keno_api_latest_*.zip"):
                old_zip.unlink()
                logger.info(f"[CLEANUP] Ancien fichier ZIP API supprimé: {old_zip.name}")
                
        except Exception as e:
            logger.warning(f"[CLEANUP] Erreur nettoyage anciens fichiers API: {e}")

if __name__ == "__main__":
    # Test du récupérateur temps réel
    fetcher = RealtimeKenoFetcher()
    
    print("[TEST] Test du récupérateur Keno temps réel")
    
    # Vérifier les données manquantes
    missing = fetcher.check_missing_recent_data()
    print(f"[TEST] Données manquantes: {len(missing['missing_tirages'])} tirages")
    
    # Informations sur les tirages d'aujourd'hui
    today_info = fetcher.get_today_expected_tirages()
    print(f"📅 Tirages aujourd'hui: {today_info}")
    
    # Mise à jour si nécessaire
    update_result = fetcher.update_data_if_needed()
    print(f"[TEST] Mise à jour: {update_result}")