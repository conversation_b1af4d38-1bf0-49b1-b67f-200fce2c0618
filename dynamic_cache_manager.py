from __future__ import annotations
import os
import pickle
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

import logging

logger = logging.getLogger(__name__)

@dataclass
class CacheStatus:
    recommendation: str  # 'cache_optimal' | 'refresh_suggested' | 'rebuild_required'
    changes_detected: bool
    changed_files: List[str]

    def to_dict(self) -> Dict[str, Any]:
        return {
            "recommendation": self.recommendation,
            "changes_detected": self.changes_detected,
            "changed_files": self.changed_files,
        }

class DynamicCacheManager:
    """
    Impl. légère de gestion de cache pour l'analyse Keno.
    Fournit l'API attendue par data_analyzer.py sans dépendances externes.
    """

    def __init__(self, cache_dir: str = "keno_data/cache") -> None:
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.processed_cache_file = self.cache_dir / "processed_data.pkl"

    # --- API utilisée par data_analyzer ---
    def get_cache_status(self) -> Dict[str, Any]:
        if not self.processed_cache_file.exists():
            return CacheStatus("rebuild_required", False, []).to_dict()
        # Sans registre sophistiqué de sources, on conseille 'refresh_suggested'
        return CacheStatus("refresh_suggested", False, []).to_dict()

    def load_cached_data(self):
        try:
            if not self.processed_cache_file.exists():
                return None
            with open(self.processed_cache_file, "rb") as f:
                payload = pickle.load(f)
            if isinstance(payload, dict) and "data" in payload:
                return payload["data"]
        except Exception as e:
            logger.warning(f"[CACHE] Impossible de charger le cache: {e}")
        return None

    def validate_data_integrity(self, df) -> Dict[str, Any]:
        try:
            import pandas as pd  # lazy
            status = "ok"
            score = 0.0
            if df is None or not isinstance(df, pd.DataFrame) or df.empty:
                return {"status": "no_data", "score": 0.0}
            rows = len(df)
            cols = len(df.columns)
            # Heuristique simple: +0.4 si >1000 tirages, +0.3 si >20 colonnes, +0.3 si colonne 'numbers' existe
            score += 0.4 if rows >= 1000 else 0.2 if rows >= 100 else 0.1
            score += 0.3 if cols >= 20 else 0.1
            score += 0.3 if "numbers" in df.columns else 0.1
            status = "ok" if score >= 0.4 else "weak"
            return {"status": status, "score": round(score, 2)}
        except Exception as e:
            logger.warning(f"[VALID] Erreur validation: {e}")
            return {"status": "error", "score": 0.0}

    def save_processed_data(self, df) -> None:
        try:
            payload = {
                "data": df,
                "timestamp": datetime.now(),
                "total_rows": int(len(df)) if hasattr(df, "__len__") else None,
            }
            with open(self.processed_cache_file, "wb") as f:
                pickle.dump(payload, f)
            logger.info(f"[CACHE] Données traitées sauvegardées: {payload['total_rows']} lignes")
        except Exception as e:
            logger.error(f"[CACHE] Erreur sauvegarde: {e}")

    def force_cache_refresh(self) -> None:
        try:
            if self.processed_cache_file.exists():
                self.processed_cache_file.unlink()
                logger.info("[CACHE] Cache supprimé")
        except Exception as e:
            logger.warning(f"[CACHE] Erreur suppression cache: {e}")

    def is_cache_valid(self, source_files: List[Path]) -> bool:
        try:
            if not self.processed_cache_file.exists():
                return False
            cache_mtime = datetime.fromtimestamp(self.processed_cache_file.stat().st_mtime)
            for p in source_files:
                try:
                    if p.exists():
                        src_mtime = datetime.fromtimestamp(p.stat().st_mtime)
                        if src_mtime > cache_mtime:
                            return False
                except Exception:
                    # Si un fichier source pose pb, considérer cache invalide par prudence
                    return False
            return True
        except Exception:
            return False

