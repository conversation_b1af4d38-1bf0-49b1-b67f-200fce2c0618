"""
Moteur de backtesting avancé pour évaluer les performances des prédictions Keno.
Implémente des métriques sophistiquées et une validation croisée temporelle.
"""

import numpy as np
import pandas as pd
from pathlib import Path
import pickle
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from concurrent.futures import ThreadPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

# Imports conditionnels
try:
    from sklearn.model_selection import TimeSeriesSplit
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

logger = logging.getLogger(__name__)

class AdvancedBacktestEngine:
    """Moteur de backtesting avancé avec métriques sophistiquées."""
    
    def __init__(self, cache_dir="keno_data/cache"):
        """Initialise le moteur de backtesting avancé."""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuration du backtesting
        self.config = {
            'min_train_size': 200,
            'test_sizes': [50, 100, 200],
            'prediction_sizes': [5, 7, 10],
            'cross_validation_splits': 5,
            'parallel_processing': True,
            'max_workers': 4,
            'confidence_levels': [0.8, 0.9, 0.95],
            'roi_simulation': True,
            'detailed_analysis': True
        }
        
        # Métriques avancées
        self.advanced_metrics = [
            'hit_rate', 'precision', 'recall', 'f1_score',
            'coverage_rate', 'diversity_score', 'consistency_score',
            'roi_score', 'risk_adjusted_return', 'sharpe_ratio',
            'max_drawdown', 'win_rate', 'average_win', 'average_loss'
        ]
        
        # Fichiers de résultats
        self.results_file = self.cache_dir / "advanced_backtest_results.pkl"
        self.summary_file = self.cache_dir / "backtest_summary.json"
        self.plots_dir = self.cache_dir / "backtest_plots"
        self.plots_dir.mkdir(exist_ok=True)
        
    def run_comprehensive_backtest(self, predictor, data: pd.DataFrame, 
                                 methods: List[str] = None) -> Dict:
        """Exécute un backtest complet avec toutes les métriques avancées."""
        logger.info("🚀 Démarrage du backtest avancé...")
        
        if methods is None:
            methods = ['enhanced_ml', 'adaptive_hybrid', 'pattern_based', 'balanced']
        
        # Préparer les données
        if len(data) < self.config['min_train_size'] + max(self.config['test_sizes']):
            raise ValueError(f"Données insuffisantes pour le backtest: {len(data)} lignes")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'config': self.config.copy(),
            'data_info': {
                'total_samples': len(data),
                'date_range': self._get_date_range(data),
                'methods_tested': methods
            },
            'method_results': {},
            'comparative_analysis': {},
            'statistical_significance': {},
            'recommendations': []
        }
        
        # Exécuter le backtest pour chaque méthode
        for method in methods:
            logger.info(f"📊 Test de la méthode: {method}")
            
            try:
                method_results = self._backtest_method_advanced(predictor, data, method)
                results['method_results'][method] = method_results
                
                logger.info(f"✅ {method}: Score global = {method_results.get('overall_score', 0):.3f}")
                
            except Exception as e:
                logger.error(f"❌ Erreur test {method}: {e}")
                results['method_results'][method] = {'error': str(e)}
        
        # Analyse comparative
        results['comparative_analysis'] = self._perform_comparative_analysis(
            results['method_results']
        )
        
        # Tests de significativité statistique
        results['statistical_significance'] = self._test_statistical_significance(
            results['method_results']
        )
        
        # Générer des recommandations
        results['recommendations'] = self._generate_recommendations(results)
        
        # Sauvegarder les résultats
        self._save_results(results)
        
        # Générer les visualisations
        self._generate_visualizations(results)
        
        logger.info("✅ Backtest avancé terminé")
        return results
    
    def _backtest_method_advanced(self, predictor, data: pd.DataFrame, method: str) -> Dict:
        """Backtest avancé pour une méthode spécifique."""
        method_results = {
            'method': method,
            'test_configurations': {},
            'time_series_validation': {},
            'performance_metrics': {},
            'stability_analysis': {},
            'risk_metrics': {}
        }
        
        # Test avec différentes configurations
        for test_size in self.config['test_sizes']:
            for pred_size in self.config['prediction_sizes']:
                config_key = f"test_{test_size}_pred_{pred_size}"
                
                config_results = self._run_configuration_test(
                    predictor, data, method, test_size, pred_size
                )
                
                method_results['test_configurations'][config_key] = config_results
        
        # Validation croisée temporelle
        if SKLEARN_AVAILABLE:
            ts_results = self._time_series_cross_validation(
                predictor, data, method
            )
            method_results['time_series_validation'] = ts_results
        
        # Calculer les métriques de performance globales
        method_results['performance_metrics'] = self._calculate_advanced_metrics(
            method_results['test_configurations']
        )
        
        # Analyse de stabilité
        method_results['stability_analysis'] = self._analyze_stability(
            method_results['test_configurations']
        )
        
        # Métriques de risque
        method_results['risk_metrics'] = self._calculate_risk_metrics(
            method_results['test_configurations']
        )
        
        # Score global
        method_results['overall_score'] = self._calculate_overall_score(method_results)
        
        return method_results
    
    def _run_configuration_test(self, predictor, data: pd.DataFrame, 
                              method: str, test_size: int, pred_size: int) -> Dict:
        """Exécute un test pour une configuration spécifique."""
        results = {
            'predictions': [],
            'actual_results': [],
            'hit_rates': [],
            'individual_scores': [],
            'timestamps': []
        }
        
        # Diviser les données
        train_end = len(data) - test_size
        train_data = data.iloc[:train_end]
        test_data = data.iloc[train_end:]
        
        # Tester sur chaque tirage de test
        for i in range(len(test_data)):
            try:
                # Utiliser les données jusqu'à ce point pour la prédiction
                current_data = pd.concat([train_data, test_data.iloc[:i]])
                
                # Générer la prédiction
                if hasattr(predictor, 'predict_adaptive') and method == 'adaptive':
                    prediction_result = predictor.predict_adaptive(pred_size)
                    predictions = prediction_result.get('predictions', [])
                elif hasattr(predictor, 'predict'):
                    predictions = predictor.predict(pred_size, method=method)
                else:
                    continue
                
                # Obtenir les résultats réels
                actual = test_data.iloc[i].get('numbers', [])
                
                # Calculer les métriques
                hit_rate = len(set(predictions) & set(actual)) / len(predictions)
                individual_score = self._calculate_individual_score(predictions, actual)
                
                # Enregistrer les résultats
                results['predictions'].append(predictions)
                results['actual_results'].append(actual)
                results['hit_rates'].append(hit_rate)
                results['individual_scores'].append(individual_score)
                results['timestamps'].append(test_data.iloc[i].get('date', ''))
                
            except Exception as e:
                logger.warning(f"Erreur test individuel: {e}")
                continue
        
        return results
    
    def _time_series_cross_validation(self, predictor, data: pd.DataFrame, 
                                    method: str) -> Dict:
        """Validation croisée temporelle."""
        if not SKLEARN_AVAILABLE:
            return {'error': 'sklearn non disponible'}
        
        tscv = TimeSeriesSplit(n_splits=self.config['cross_validation_splits'])
        cv_results = []
        
        for fold, (train_idx, test_idx) in enumerate(tscv.split(data)):
            try:
                train_data = data.iloc[train_idx]
                test_data = data.iloc[test_idx]
                
                # Tester sur un échantillon du fold de test
                sample_size = min(20, len(test_data))
                test_sample = test_data.sample(n=sample_size, random_state=42)
                
                fold_scores = []
                for _, test_row in test_sample.iterrows():
                    # Prédiction
                    if hasattr(predictor, 'predict'):
                        predictions = predictor.predict(7, method=method)
                        actual = test_row.get('numbers', [])
                        
                        score = len(set(predictions) & set(actual)) / len(predictions)
                        fold_scores.append(score)
                
                cv_results.append({
                    'fold': fold,
                    'train_size': len(train_data),
                    'test_size': len(test_data),
                    'mean_score': np.mean(fold_scores) if fold_scores else 0,
                    'std_score': np.std(fold_scores) if fold_scores else 0
                })
                
            except Exception as e:
                logger.warning(f"Erreur fold {fold}: {e}")
                continue
        
        return {
            'cv_results': cv_results,
            'mean_cv_score': np.mean([r['mean_score'] for r in cv_results]),
            'std_cv_score': np.std([r['mean_score'] for r in cv_results])
        }
    
    def _calculate_advanced_metrics(self, test_configurations: Dict) -> Dict:
        """Calcule les métriques avancées."""
        all_hit_rates = []
        all_scores = []
        
        for config, results in test_configurations.items():
            all_hit_rates.extend(results.get('hit_rates', []))
            all_scores.extend(results.get('individual_scores', []))
        
        if not all_hit_rates:
            return {'error': 'Aucune donnée pour calculer les métriques'}
        
        metrics = {
            'mean_hit_rate': np.mean(all_hit_rates),
            'std_hit_rate': np.std(all_hit_rates),
            'median_hit_rate': np.median(all_hit_rates),
            'max_hit_rate': np.max(all_hit_rates),
            'min_hit_rate': np.min(all_hit_rates),
            'hit_rate_percentiles': {
                '25th': np.percentile(all_hit_rates, 25),
                '75th': np.percentile(all_hit_rates, 75),
                '90th': np.percentile(all_hit_rates, 90)
            },
            'consistency_score': 1.0 - (np.std(all_hit_rates) / np.mean(all_hit_rates)) if np.mean(all_hit_rates) > 0 else 0,
            'win_rate': len([hr for hr in all_hit_rates if hr > 0.2]) / len(all_hit_rates),
            'excellent_rate': len([hr for hr in all_hit_rates if hr > 0.4]) / len(all_hit_rates)
        }
        
        # Métriques de distribution
        metrics['distribution_analysis'] = {
            'skewness': self._calculate_skewness(all_hit_rates),
            'kurtosis': self._calculate_kurtosis(all_hit_rates),
            'normality_test': self._test_normality(all_hit_rates)
        }
        
        return metrics
    
    def _analyze_stability(self, test_configurations: Dict) -> Dict:
        """Analyse la stabilité des performances."""
        stability_metrics = {}
        
        for config, results in test_configurations.items():
            hit_rates = results.get('hit_rates', [])
            if len(hit_rates) < 10:
                continue
            
            # Diviser en segments temporels
            segment_size = len(hit_rates) // 4
            segments = [hit_rates[i:i+segment_size] for i in range(0, len(hit_rates), segment_size)]
            segments = [seg for seg in segments if len(seg) >= 5]
            
            if len(segments) >= 2:
                segment_means = [np.mean(seg) for seg in segments]
                
                stability_metrics[config] = {
                    'segment_means': segment_means,
                    'stability_coefficient': 1.0 - (np.std(segment_means) / np.mean(segment_means)) if np.mean(segment_means) > 0 else 0,
                    'trend_analysis': self._analyze_trend(segment_means),
                    'volatility': np.std(hit_rates) / np.mean(hit_rates) if np.mean(hit_rates) > 0 else float('inf')
                }
        
        return stability_metrics
    
    def _calculate_risk_metrics(self, test_configurations: Dict) -> Dict:
        """Calcule les métriques de risque."""
        all_returns = []
        
        for config, results in test_configurations.items():
            hit_rates = results.get('hit_rates', [])
            # Simuler des "retours" basés sur les hit rates
            returns = [(hr - 0.2) * 100 for hr in hit_rates]  # Normaliser autour de 20%
            all_returns.extend(returns)
        
        if not all_returns:
            return {'error': 'Aucune donnée pour calculer les risques'}
        
        returns_array = np.array(all_returns)
        
        risk_metrics = {
            'value_at_risk_95': np.percentile(returns_array, 5),
            'expected_shortfall': np.mean(returns_array[returns_array <= np.percentile(returns_array, 5)]),
            'max_drawdown': self._calculate_max_drawdown(returns_array),
            'volatility': np.std(returns_array),
            'sharpe_ratio': np.mean(returns_array) / np.std(returns_array) if np.std(returns_array) > 0 else 0,
            'sortino_ratio': self._calculate_sortino_ratio(returns_array),
            'calmar_ratio': np.mean(returns_array) / abs(self._calculate_max_drawdown(returns_array)) if self._calculate_max_drawdown(returns_array) != 0 else 0
        }
        
        return risk_metrics
    
    def _calculate_overall_score(self, method_results: Dict) -> float:
        """Calcule un score global pour la méthode."""
        weights = {
            'performance': 0.4,
            'stability': 0.3,
            'risk_adjusted': 0.2,
            'consistency': 0.1
        }
        
        # Score de performance
        perf_metrics = method_results.get('performance_metrics', {})
        performance_score = perf_metrics.get('mean_hit_rate', 0) * 2  # Normaliser
        
        # Score de stabilité
        stability_metrics = method_results.get('stability_analysis', {})
        stability_scores = [m.get('stability_coefficient', 0) for m in stability_metrics.values()]
        stability_score = np.mean(stability_scores) if stability_scores else 0
        
        # Score ajusté au risque
        risk_metrics = method_results.get('risk_metrics', {})
        sharpe = risk_metrics.get('sharpe_ratio', 0)
        risk_adjusted_score = max(0, min(1, (sharpe + 1) / 2))  # Normaliser Sharpe
        
        # Score de consistance
        consistency_score = perf_metrics.get('consistency_score', 0)
        
        # Calculer le score pondéré
        overall_score = (
            performance_score * weights['performance'] +
            stability_score * weights['stability'] +
            risk_adjusted_score * weights['risk_adjusted'] +
            consistency_score * weights['consistency']
        )
        
        return min(1.0, max(0.0, overall_score))
    
    def _perform_comparative_analysis(self, method_results: Dict) -> Dict:
        """Effectue une analyse comparative entre les méthodes."""
        if len(method_results) < 2:
            return {'error': 'Au moins 2 méthodes nécessaires pour la comparaison'}
        
        comparison = {
            'ranking': [],
            'performance_comparison': {},
            'statistical_tests': {},
            'best_method': None,
            'improvement_opportunities': []
        }
        
        # Classement par score global
        method_scores = []
        for method, results in method_results.items():
            if 'error' not in results:
                score = results.get('overall_score', 0)
                method_scores.append((method, score))
        
        method_scores.sort(key=lambda x: x[1], reverse=True)
        comparison['ranking'] = method_scores
        
        if method_scores:
            comparison['best_method'] = method_scores[0][0]
        
        # Comparaison détaillée des performances
        for metric in ['mean_hit_rate', 'consistency_score', 'win_rate']:
            metric_comparison = {}
            for method, results in method_results.items():
                if 'error' not in results:
                    perf_metrics = results.get('performance_metrics', {})
                    metric_comparison[method] = perf_metrics.get(metric, 0)
            
            comparison['performance_comparison'][metric] = metric_comparison
        
        return comparison
    
    def _test_statistical_significance(self, method_results: Dict) -> Dict:
        """Teste la significativité statistique des différences."""
        significance_tests = {}
        
        # Extraire les hit rates pour chaque méthode
        method_hit_rates = {}
        for method, results in method_results.items():
            if 'error' not in results:
                all_hit_rates = []
                for config_results in results.get('test_configurations', {}).values():
                    all_hit_rates.extend(config_results.get('hit_rates', []))
                if all_hit_rates:
                    method_hit_rates[method] = all_hit_rates
        
        # Tests de significativité entre paires de méthodes
        methods = list(method_hit_rates.keys())
        for i, method1 in enumerate(methods):
            for method2 in methods[i+1:]:
                try:
                    # Test t de Student (approximation)
                    data1 = np.array(method_hit_rates[method1])
                    data2 = np.array(method_hit_rates[method2])
                    
                    # Calculer la statistique t
                    mean_diff = np.mean(data1) - np.mean(data2)
                    pooled_std = np.sqrt((np.var(data1) + np.var(data2)) / 2)
                    t_stat = mean_diff / (pooled_std * np.sqrt(2/len(data1))) if pooled_std > 0 else 0
                    
                    # Approximation de la p-value (très simplifiée)
                    p_value = 2 * (1 - abs(t_stat) / (abs(t_stat) + 1))  # Approximation grossière
                    
                    significance_tests[f"{method1}_vs_{method2}"] = {
                        'mean_difference': mean_diff,
                        't_statistic': t_stat,
                        'p_value_approx': p_value,
                        'significant': p_value < 0.05,
                        'effect_size': abs(mean_diff) / pooled_std if pooled_std > 0 else 0
                    }
                    
                except Exception as e:
                    logger.warning(f"Erreur test significativité {method1} vs {method2}: {e}")
        
        return significance_tests
    
    def _generate_recommendations(self, results: Dict) -> List[str]:
        """Génère des recommandations basées sur les résultats."""
        recommendations = []
        
        # Analyser les résultats
        comparative = results.get('comparative_analysis', {})
        best_method = comparative.get('best_method')
        
        if best_method:
            recommendations.append(f"✅ Utiliser prioritairement la méthode '{best_method}' (meilleure performance globale)")
        
        # Analyser les performances
        method_results = results.get('method_results', {})
        low_performers = []
        high_volatility = []
        
        for method, method_data in method_results.items():
            if 'error' in method_data:
                continue
                
            perf_metrics = method_data.get('performance_metrics', {})
            risk_metrics = method_data.get('risk_metrics', {})
            
            mean_hit_rate = perf_metrics.get('mean_hit_rate', 0)
            volatility = risk_metrics.get('volatility', 0)
            
            if mean_hit_rate < 0.25:
                low_performers.append(method)
            
            if volatility > 20:  # Seuil arbitraire
                high_volatility.append(method)
        
        if low_performers:
            recommendations.append(f"⚠️ Améliorer ou remplacer les méthodes sous-performantes: {', '.join(low_performers)}")
        
        if high_volatility:
            recommendations.append(f"📊 Stabiliser les méthodes volatiles: {', '.join(high_volatility)}")
        
        # Recommandations générales
        recommendations.append("🔄 Effectuer un backtest régulier pour maintenir les performances")
        recommendations.append("📈 Considérer l'ensemble des méthodes pour diversifier les risques")
        
        return recommendations
    
    def _save_results(self, results: Dict):
        """Sauvegarde les résultats du backtest."""
        try:
            # Sauvegarder les résultats complets
            with open(self.results_file, 'wb') as f:
                pickle.dump(results, f)
            
            # Créer un résumé JSON
            summary = {
                'timestamp': results['timestamp'],
                'methods_tested': results['data_info']['methods_tested'],
                'best_method': results['comparative_analysis'].get('best_method'),
                'ranking': results['comparative_analysis'].get('ranking', []),
                'recommendations': results['recommendations']
            }
            
            with open(self.summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info("✅ Résultats de backtest sauvegardés")
            
        except Exception as e:
            logger.error(f"Erreur sauvegarde backtest: {e}")
    
    def _generate_visualizations(self, results: Dict):
        """Génère des visualisations des résultats."""
        try:
            # Graphique de comparaison des performances
            self._plot_performance_comparison(results)
            
            # Graphique de stabilité temporelle
            self._plot_stability_analysis(results)
            
            # Graphique de distribution des hit rates
            self._plot_hit_rate_distribution(results)
            
            logger.info("📊 Visualisations générées")
            
        except Exception as e:
            logger.warning(f"Erreur génération visualisations: {e}")
    
    def _plot_performance_comparison(self, results: Dict):
        """Graphique de comparaison des performances."""
        method_results = results.get('method_results', {})
        
        methods = []
        scores = []
        hit_rates = []
        
        for method, method_data in method_results.items():
            if 'error' in method_data:
                continue
            
            methods.append(method)
            scores.append(method_data.get('overall_score', 0))
            
            perf_metrics = method_data.get('performance_metrics', {})
            hit_rates.append(perf_metrics.get('mean_hit_rate', 0))
        
        if methods:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # Score global
            ax1.bar(methods, scores, color='skyblue', alpha=0.7)
            ax1.set_title('Score Global par Méthode')
            ax1.set_ylabel('Score Global')
            ax1.tick_params(axis='x', rotation=45)
            
            # Hit rate moyen
            ax2.bar(methods, hit_rates, color='lightcoral', alpha=0.7)
            ax2.set_title('Hit Rate Moyen par Méthode')
            ax2.set_ylabel('Hit Rate Moyen')
            ax2.tick_params(axis='x', rotation=45)
            
            plt.tight_layout()
            plt.savefig(self.plots_dir / 'performance_comparison.png', dpi=300, bbox_inches='tight')
            plt.close()
    
    def _plot_stability_analysis(self, results: Dict):
        """Graphique d'analyse de stabilité."""
        # Implementation simplifiée
        pass
    
    def _plot_hit_rate_distribution(self, results: Dict):
        """Graphique de distribution des hit rates."""
        # Implementation simplifiée
        pass
    
    # Méthodes utilitaires
    def _get_date_range(self, data: pd.DataFrame) -> Dict:
        """Obtient la plage de dates des données."""
        try:
            dates = pd.to_datetime(data['date'], errors='coerce')
            return {
                'start': dates.min().isoformat() if not dates.isna().all() else 'N/A',
                'end': dates.max().isoformat() if not dates.isna().all() else 'N/A'
            }
        except:
            return {'start': 'N/A', 'end': 'N/A'}
    
    def _calculate_individual_score(self, predictions: List[int], actual: List[int]) -> float:
        """Calcule un score individuel pour une prédiction."""
        if not predictions or not actual:
            return 0.0
        
        hits = len(set(predictions) & set(actual))
        hit_rate = hits / len(predictions)
        
        # Bonus pour les hits multiples
        bonus = min(0.2, hits * 0.05)
        
        return min(1.0, hit_rate + bonus)
    
    def _calculate_skewness(self, data: List[float]) -> float:
        """Calcule l'asymétrie."""
        if len(data) < 3:
            return 0.0
        
        data_array = np.array(data)
        mean = np.mean(data_array)
        std = np.std(data_array)
        
        if std == 0:
            return 0.0
        
        skewness = np.mean(((data_array - mean) / std) ** 3)
        return skewness
    
    def _calculate_kurtosis(self, data: List[float]) -> float:
        """Calcule l'aplatissement."""
        if len(data) < 4:
            return 0.0
        
        data_array = np.array(data)
        mean = np.mean(data_array)
        std = np.std(data_array)
        
        if std == 0:
            return 0.0
        
        kurtosis = np.mean(((data_array - mean) / std) ** 4) - 3
        return kurtosis
    
    def _test_normality(self, data: List[float]) -> Dict:
        """Test simple de normalité."""
        if len(data) < 8:
            return {'test': 'insufficient_data', 'p_value': None, 'normal': None}
        
        # Test très simplifié basé sur l'asymétrie et l'aplatissement
        skewness = abs(self._calculate_skewness(data))
        kurtosis = abs(self._calculate_kurtosis(data))
        
        # Critères approximatifs
        normal = skewness < 1.0 and kurtosis < 2.0
        
        return {
            'test': 'simplified_normality',
            'skewness': skewness,
            'kurtosis': kurtosis,
            'normal': normal
        }
    
    def _analyze_trend(self, values: List[float]) -> Dict:
        """Analyse la tendance d'une série."""
        if len(values) < 3:
            return {'trend': 'insufficient_data'}
        
        # Régression linéaire simple
        x = np.arange(len(values))
        y = np.array(values)
        
        # Calculer la pente
        slope = np.polyfit(x, y, 1)[0]
        
        if slope > 0.01:
            trend = 'increasing'
        elif slope < -0.01:
            trend = 'decreasing'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'slope': slope,
            'r_squared': np.corrcoef(x, y)[0, 1] ** 2 if len(x) > 1 else 0
        }
    
    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """Calcule le drawdown maximum."""
        cumulative = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = cumulative - running_max
        return np.min(drawdown)
    
    def _calculate_sortino_ratio(self, returns: np.ndarray) -> float:
        """Calcule le ratio de Sortino."""
        mean_return = np.mean(returns)
        negative_returns = returns[returns < 0]
        
        if len(negative_returns) == 0:
            return float('inf') if mean_return > 0 else 0
        
        downside_deviation = np.std(negative_returns)
        
        if downside_deviation == 0:
            return float('inf') if mean_return > 0 else 0
        
        return mean_return / downside_deviation


if __name__ == "__main__":
    # Test du moteur de backtesting avancé
    print("🧪 Test du moteur de backtesting avancé...")
    
    try:
        engine = AdvancedBacktestEngine()
        
        # Créer des données de test
        test_data = []
        np.random.seed(42)
        for i in range(300):
            numbers = sorted(np.random.choice(range(1, 71), size=20, replace=False))
            test_data.append({
                'numbers': numbers,
                'date': pd.Timestamp('2023-01-01') + pd.Timedelta(days=i)
            })
        
        test_df = pd.DataFrame(test_data)
        
        print(f"✅ Données de test créées: {len(test_df)} tirages")
        print("Configuration du backtest:")
        for key, value in engine.config.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
