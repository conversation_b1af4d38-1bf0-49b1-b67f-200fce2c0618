"""
Système de validation robuste pour le prédicteur Keno optimisé.
Teste et valide la fiabilité des prédictions avec des métriques détaillées.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

from optimized_keno_predictor import OptimizedKenoPredictor

logger = logging.getLogger(__name__)

class KenoValidator:
    """Système de validation pour les prédictions Keno."""
    
    def __init__(self, predictor: OptimizedKenoPredictor):
        """
        Initialise le validateur.
        
        Args:
            predictor: Instance du prédicteur à valider
        """
        self.predictor = predictor
        self.validation_results = []
        self.test_results = {}
        
        # Configuration des tests
        self.test_config = {
            'backtest_periods': [30, 60, 90],  # Périodes de backtest en jours
            'prediction_methods': ['ensemble', 'frequency', 'pattern', 'trend', 'statistical', 'momentum'],
            'prediction_sizes': [5, 7, 10],  # Nombres de prédictions à tester
            'confidence_thresholds': [0.5, 0.6, 0.7, 0.8],
            'min_data_points': 50
        }
        
        logger.info("✅ Validateur Keno initialisé")
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """
        Exécute une validation complète du système de prédiction.
        
        Returns:
            Dict: Résultats complets de validation
        """
        logger.info("🧪 Démarrage de la validation complète...")
        
        validation_report = {
            'timestamp': datetime.now().isoformat(),
            'data_quality_check': self._check_data_quality(),
            'method_comparison': self._compare_prediction_methods(),
            'backtest_results': self._run_backtests(),
            'confidence_analysis': self._analyze_confidence_scores(),
            'stability_test': self._test_prediction_stability(),
            'performance_metrics': self._calculate_performance_metrics(),
            'recommendations': []
        }
        
        # Générer des recommandations
        validation_report['recommendations'] = self._generate_recommendations(validation_report)
        
        # Sauvegarder les résultats
        self._save_validation_report(validation_report)
        
        logger.info("✅ Validation complète terminée")
        return validation_report
    
    def _check_data_quality(self) -> Dict[str, Any]:
        """Vérifie la qualité des données historiques."""
        data = self.predictor.historical_data
        
        if not data:
            return {'status': 'error', 'message': 'Aucune donnée disponible'}
        
        quality_metrics = {
            'total_draws': len(data),
            'date_range': {
                'start': data[0]['date'] if data else None,
                'end': data[-1]['date'] if data else None
            },
            'data_completeness': {},
            'number_distribution': {},
            'temporal_consistency': {}
        }
        
        # Vérifier la complétude des données
        complete_draws = sum(1 for draw in data if 'numbers' in draw and len(draw['numbers']) > 0)
        quality_metrics['data_completeness'] = {
            'complete_draws': complete_draws,
            'completion_rate': complete_draws / len(data) if data else 0,
            'average_numbers_per_draw': np.mean([len(draw.get('numbers', [])) for draw in data])
        }
        
        # Analyser la distribution des numéros
        all_numbers = []
        for draw in data:
            all_numbers.extend(draw.get('numbers', []))
        
        if all_numbers:
            unique_numbers = set(all_numbers)
            quality_metrics['number_distribution'] = {
                'unique_numbers_count': len(unique_numbers),
                'coverage': len(unique_numbers) / 70,  # Couverture sur 1-70
                'most_frequent': max(set(all_numbers), key=all_numbers.count),
                'least_frequent': min(set(all_numbers), key=all_numbers.count),
                'distribution_uniformity': self._calculate_uniformity(all_numbers)
            }
        
        # Vérifier la consistance temporelle
        dates = [pd.to_datetime(draw['date']) for draw in data if 'date' in draw]
        if len(dates) > 1:
            date_gaps = [(dates[i+1] - dates[i]).days for i in range(len(dates)-1)]
            quality_metrics['temporal_consistency'] = {
                'average_gap_days': np.mean(date_gaps),
                'max_gap_days': max(date_gaps),
                'regular_intervals': np.std(date_gaps) < 2  # Écart-type < 2 jours
            }
        
        # Évaluation globale
        quality_score = 0
        if quality_metrics['data_completeness']['completion_rate'] > 0.9:
            quality_score += 0.3
        if quality_metrics['number_distribution']['coverage'] > 0.8:
            quality_score += 0.3
        if len(data) >= 100:
            quality_score += 0.2
        if quality_metrics['temporal_consistency'].get('regular_intervals', False):
            quality_score += 0.2
        
        quality_metrics['overall_quality_score'] = quality_score
        quality_metrics['quality_rating'] = self._get_quality_rating(quality_score)
        
        return quality_metrics
    
    def _compare_prediction_methods(self) -> Dict[str, Any]:
        """Compare les différentes méthodes de prédiction."""
        if len(self.predictor.historical_data) < self.test_config['min_data_points']:
            return {'error': 'Données insuffisantes pour la comparaison'}
        
        method_comparison = {}
        
        for method in self.test_config['prediction_methods']:
            logger.info(f"🔍 Test de la méthode: {method}")
            
            try:
                # Tester la méthode avec différentes tailles de prédiction
                method_results = {}
                
                for pred_size in self.test_config['prediction_sizes']:
                    # Faire plusieurs prédictions pour évaluer la consistance
                    predictions = []
                    confidences = []
                    
                    for _ in range(5):  # 5 prédictions par taille
                        result = self.predictor.predict_next_draw(pred_size, method)
                        predictions.append(result['predictions'])
                        confidences.append(result['confidence'])
                    
                    method_results[f'size_{pred_size}'] = {
                        'predictions': predictions,
                        'average_confidence': np.mean(confidences),
                        'confidence_stability': 1 - np.std(confidences),  # Plus stable = moins de variance
                        'prediction_diversity': self._calculate_prediction_diversity(predictions)
                    }
                
                method_comparison[method] = method_results
                
            except Exception as e:
                logger.warning(f"Erreur test méthode {method}: {e}")
                method_comparison[method] = {'error': str(e)}
        
        # Analyser les résultats
        method_ranking = self._rank_methods(method_comparison)
        
        return {
            'method_results': method_comparison,
            'method_ranking': method_ranking,
            'best_method': method_ranking[0] if method_ranking else None
        }
    
    def _run_backtests(self) -> Dict[str, Any]:
        """Exécute des backtests sur différentes périodes."""
        data = self.predictor.historical_data
        
        if len(data) < 100:
            return {'error': 'Données insuffisantes pour le backtest'}
        
        backtest_results = {}
        
        for period in self.test_config['backtest_periods']:
            if len(data) > period + 20:  # Assurer suffisamment de données
                logger.info(f"📊 Backtest sur {period} jours")
                
                # Diviser les données
                split_point = len(data) - period
                train_data = data[:split_point]
                test_data = data[split_point:]
                
                # Créer un prédicteur temporaire avec les données d'entraînement
                temp_predictor = OptimizedKenoPredictor()
                temp_predictor.historical_data = train_data
                
                # Tester les prédictions
                period_results = []
                
                for i, actual_draw in enumerate(test_data):
                    if i % 5 == 0:  # Tester tous les 5 tirages pour performance
                        try:
                            # Mettre à jour les données du prédicteur temporaire
                            temp_predictor.historical_data = train_data + test_data[:i]
                            
                            # Faire une prédiction
                            prediction_result = temp_predictor.predict_next_draw(7, 'ensemble')
                            
                            # Valider contre le tirage réel
                            validation = temp_predictor.validate_predictions(
                                actual_draw['numbers'], 
                                prediction_result['predictions']
                            )
                            
                            period_results.append({
                                'draw_index': i,
                                'prediction': prediction_result['predictions'],
                                'actual': actual_draw['numbers'],
                                'hits': validation['hits'],
                                'hit_rate': validation['hit_rate'],
                                'confidence': prediction_result['confidence']
                            })
                            
                        except Exception as e:
                            logger.warning(f"Erreur backtest draw {i}: {e}")
                
                # Calculer les métriques du backtest
                if period_results:
                    hit_rates = [r['hit_rate'] for r in period_results]
                    confidences = [r['confidence'] for r in period_results]
                    
                    backtest_results[f'{period}_days'] = {
                        'total_tests': len(period_results),
                        'average_hit_rate': np.mean(hit_rates),
                        'best_hit_rate': max(hit_rates),
                        'worst_hit_rate': min(hit_rates),
                        'hit_rate_stability': 1 - np.std(hit_rates),
                        'average_confidence': np.mean(confidences),
                        'confidence_accuracy_correlation': np.corrcoef(confidences, hit_rates)[0,1] if len(confidences) > 1 else 0,
                        'detailed_results': period_results
                    }
        
        return backtest_results
    
    def _analyze_confidence_scores(self) -> Dict[str, Any]:
        """Analyse la fiabilité des scores de confiance."""
        confidence_analysis = {}
        
        # Tester différents seuils de confiance
        for threshold in self.test_config['confidence_thresholds']:
            # Faire plusieurs prédictions avec ce seuil
            high_confidence_predictions = []
            low_confidence_predictions = []
            
            for _ in range(20):  # 20 tests par seuil
                try:
                    result = self.predictor.predict_next_draw(7, 'ensemble')
                    
                    if result['confidence'] >= threshold:
                        high_confidence_predictions.append(result)
                    else:
                        low_confidence_predictions.append(result)
                        
                except Exception as e:
                    logger.warning(f"Erreur test confiance: {e}")
            
            confidence_analysis[f'threshold_{threshold}'] = {
                'high_confidence_count': len(high_confidence_predictions),
                'low_confidence_count': len(low_confidence_predictions),
                'high_confidence_avg': np.mean([p['confidence'] for p in high_confidence_predictions]) if high_confidence_predictions else 0,
                'low_confidence_avg': np.mean([p['confidence'] for p in low_confidence_predictions]) if low_confidence_predictions else 0
            }
        
        return confidence_analysis
    
    def _test_prediction_stability(self) -> Dict[str, Any]:
        """Teste la stabilité des prédictions."""
        stability_results = {}
        
        # Test de reproductibilité
        predictions_set1 = []
        predictions_set2 = []
        
        for _ in range(10):
            pred1 = self.predictor.predict_next_draw(7, 'ensemble')
            pred2 = self.predictor.predict_next_draw(7, 'ensemble')
            
            predictions_set1.append(pred1['predictions'])
            predictions_set2.append(pred2['predictions'])
        
        # Calculer la similarité entre les sets
        similarity_scores = []
        for p1, p2 in zip(predictions_set1, predictions_set2):
            overlap = len(set(p1) & set(p2))
            similarity = overlap / len(p1)
            similarity_scores.append(similarity)
        
        stability_results['reproducibility'] = {
            'average_similarity': np.mean(similarity_scores),
            'similarity_stability': 1 - np.std(similarity_scores)
        }
        
        # Test de sensibilité aux données
        original_data_size = len(self.predictor.historical_data)
        
        # Tester avec différentes tailles de données
        data_sensitivity = {}
        for reduction in [0.1, 0.2, 0.3]:  # Réduire de 10%, 20%, 30%
            reduced_size = int(original_data_size * (1 - reduction))
            
            # Sauvegarder les données originales
            original_data = self.predictor.historical_data.copy()
            
            # Réduire les données
            self.predictor.historical_data = original_data[-reduced_size:]
            
            # Faire des prédictions
            reduced_predictions = []
            for _ in range(5):
                pred = self.predictor.predict_next_draw(7, 'ensemble')
                reduced_predictions.append(pred['predictions'])
            
            # Restaurer les données originales
            self.predictor.historical_data = original_data
            
            # Calculer la différence
            original_pred = self.predictor.predict_next_draw(7, 'ensemble')['predictions']
            
            similarities = []
            for reduced_pred in reduced_predictions:
                overlap = len(set(original_pred) & set(reduced_pred))
                similarity = overlap / len(original_pred)
                similarities.append(similarity)
            
            data_sensitivity[f'reduction_{int(reduction*100)}%'] = {
                'average_similarity': np.mean(similarities),
                'data_robustness': 1 - np.std(similarities)
            }
        
        stability_results['data_sensitivity'] = data_sensitivity
        
        return stability_results
    
    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calcule les métriques de performance globales."""
        return {
            'predictor_performance': self.predictor.get_performance_summary(),
            'validation_summary': {
                'total_validations': len(self.validation_results),
                'validation_timestamp': datetime.now().isoformat()
            }
        }
    
    def _generate_recommendations(self, validation_report: Dict) -> List[str]:
        """Génère des recommandations basées sur les résultats de validation."""
        recommendations = []
        
        # Recommandations basées sur la qualité des données
        data_quality = validation_report.get('data_quality_check', {})
        quality_score = data_quality.get('overall_quality_score', 0)
        
        if quality_score < 0.5:
            recommendations.append("🚨 CRITIQUE: Améliorer la qualité des données historiques")
        elif quality_score < 0.7:
            recommendations.append("⚠️ Augmenter la quantité de données historiques")
        
        # Recommandations basées sur les méthodes
        method_comparison = validation_report.get('method_comparison', {})
        best_method = method_comparison.get('best_method')
        
        if best_method:
            recommendations.append(f"✅ Utiliser prioritairement la méthode '{best_method}'")
        
        # Recommandations basées sur les backtests
        backtest_results = validation_report.get('backtest_results', {})
        if backtest_results and not backtest_results.get('error'):
            avg_hit_rates = []
            for period_key, results in backtest_results.items():
                if isinstance(results, dict) and 'average_hit_rate' in results:
                    avg_hit_rates.append(results['average_hit_rate'])
            
            if avg_hit_rates:
                overall_avg = np.mean(avg_hit_rates)
                if overall_avg > 0.3:
                    recommendations.append(f"🎯 Performance satisfaisante: {overall_avg:.1%} de précision moyenne")
                elif overall_avg > 0.2:
                    recommendations.append("📈 Performance modérée - optimisation recommandée")
                else:
                    recommendations.append("🔧 Performance faible - révision des algorithmes nécessaire")
        
        # Recommandations générales
        recommendations.append("🔄 Effectuer une validation régulière (hebdomadaire)")
        recommendations.append("📊 Surveiller les métriques de confiance")
        
        return recommendations
    
    def _calculate_uniformity(self, numbers: List[int]) -> float:
        """Calcule l'uniformité de la distribution des numéros."""
        if not numbers:
            return 0
        
        from collections import Counter
        counts = Counter(numbers)
        expected_freq = len(numbers) / 70  # Fréquence théorique
        
        # Calculer l'écart-type des fréquences
        frequencies = list(counts.values())
        std_dev = np.std(frequencies)
        
        # Normaliser (plus proche de 1 = plus uniforme)
        uniformity = 1 / (1 + std_dev / expected_freq) if expected_freq > 0 else 0
        return uniformity
    
    def _calculate_prediction_diversity(self, predictions: List[List[int]]) -> float:
        """Calcule la diversité entre plusieurs prédictions."""
        if len(predictions) < 2:
            return 0
        
        # Calculer la similarité moyenne entre toutes les paires
        similarities = []
        for i in range(len(predictions)):
            for j in range(i+1, len(predictions)):
                overlap = len(set(predictions[i]) & set(predictions[j]))
                similarity = overlap / len(predictions[i])
                similarities.append(similarity)
        
        # Diversité = 1 - similarité moyenne
        return 1 - np.mean(similarities) if similarities else 0
    
    def _rank_methods(self, method_comparison: Dict) -> List[str]:
        """Classe les méthodes par performance."""
        method_scores = {}
        
        for method, results in method_comparison.items():
            if 'error' in results:
                method_scores[method] = 0
                continue
            
            # Calculer un score composite
            total_score = 0
            count = 0
            
            for size_key, size_results in results.items():
                if isinstance(size_results, dict):
                    confidence = size_results.get('average_confidence', 0)
                    stability = size_results.get('confidence_stability', 0)
                    diversity = size_results.get('prediction_diversity', 0)
                    
                    # Score pondéré
                    score = confidence * 0.5 + stability * 0.3 + diversity * 0.2
                    total_score += score
                    count += 1
            
            method_scores[method] = total_score / count if count > 0 else 0
        
        # Trier par score décroissant
        return sorted(method_scores.keys(), key=lambda x: method_scores[x], reverse=True)
    
    def _get_quality_rating(self, score: float) -> str:
        """Convertit un score de qualité en rating."""
        if score >= 0.8:
            return "Excellent"
        elif score >= 0.6:
            return "Bon"
        elif score >= 0.4:
            return "Moyen"
        elif score >= 0.2:
            return "Faible"
        else:
            return "Très faible"
    
    def _save_validation_report(self, report: Dict):
        """Sauvegarde le rapport de validation."""
        try:
            report_file = Path("keno_data/cache/validation_report.json")
            report_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"✅ Rapport de validation sauvegardé: {report_file}")
            
        except Exception as e:
            logger.error(f"Erreur sauvegarde rapport: {e}")


if __name__ == "__main__":
    # Test du validateur
    print("🧪 Test du système de validation Keno")
    
    try:
        # Créer un prédicteur
        predictor = OptimizedKenoPredictor()
        
        # Créer le validateur
        validator = KenoValidator(predictor)
        
        # Exécuter la validation
        results = validator.run_comprehensive_validation()
        
        # Afficher les résultats principaux
        print(f"\n📊 RÉSULTATS DE VALIDATION")
        print(f"=" * 50)
        
        data_quality = results.get('data_quality_check', {})
        print(f"📈 Qualité des données: {data_quality.get('quality_rating', 'N/A')}")
        print(f"📊 Tirages disponibles: {data_quality.get('total_draws', 0)}")
        
        method_comparison = results.get('method_comparison', {})
        best_method = method_comparison.get('best_method')
        print(f"🏆 Meilleure méthode: {best_method}")
        
        backtest = results.get('backtest_results', {})
        if backtest and not backtest.get('error'):
            print(f"📈 Résultats backtest disponibles pour {len(backtest)} périodes")
        
        recommendations = results.get('recommendations', [])
        print(f"\n💡 RECOMMANDATIONS:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
        
        print(f"\n✅ Validation terminée avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors de la validation: {e}")
        import traceback
        traceback.print_exc()
