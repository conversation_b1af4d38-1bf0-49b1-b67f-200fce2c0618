#!/usr/bin/env python3
"""
Test du système de double roulement Chance + Chance.
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire actuel au path
sys.path.insert(0, os.path.dirname(__file__))

def test_double_roll_basic():
    """Test basique du double roulement."""
    print("Test du double roulement basique...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        # Créer un prédicteur avec des données simulées
        analyzer = KenoDataAnalyzer()
        predictor = KenoPredictor(analyzer)
        
        # Initialiser avec des poids par défaut
        predictor.initialize_weights()
        
        print("  Predictor cree avec succes")
        
        # Test d'activation du double roulement
        print("  Test activation double roulement...")
        predictor.enable_double_roll(True)
        print(f"    Double roulement actif: {predictor.double_roll_enabled}")
        
        # Test d'un roulement chance simple
        print("  Test roulement chance...")
        chance_result = predictor.perform_chance_roll('balanced')
        print(f"    Roulement chance: {chance_result}")
        print(f"    Taille: {len(chance_result)} numeros")
        
        # Test calcul d'influence après 2 roulements
        print("  Test second roulement...")
        chance_result2 = predictor.perform_chance_roll('balanced')
        print(f"    Second roulement: {chance_result2}")
        
        print("  Test calcul influence...")
        influence = predictor.calculate_double_roll_influence()
        print(f"    Influence calculee: {len(influence)} numeros affectes")
        print(f"    Exemples influence: {dict(list(influence.items())[:5])}")
        
        return True
        
    except Exception as e:
        print(f"  ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_double_roll_prediction():
    """Test de prédiction avec double roulement."""
    print("\nTest prediction avec double roulement...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        analyzer = KenoDataAnalyzer()
        predictor = KenoPredictor(analyzer)
        predictor.initialize_weights()
        
        # Test de la méthode predict_with_double_roll
        print("  Test predict_with_double_roll...")
        predictions, roll_details = predictor.predict_with_double_roll(7, 'balanced', 2)
        
        print(f"    Predictions generees: {len(predictions)} grilles")
        for i, pred in enumerate(predictions, 1):
            print(f"      Grille {i}: {sorted(pred)}")
        
        print(f"    Total roulements: {roll_details['total_rolls']}")
        print(f"    Methode utilisee: {roll_details['method_used']}")
        print(f"    Grilles generees: {roll_details['sets_generated']}")
        
        # Afficher l'historique des roulements
        print("    Historique roulements:")
        for i, roll in enumerate(roll_details['roll_history'], 1):
            print(f"      Roulement {i}: {roll['numbers']} (methode: {roll['method']})")
        
        return True
        
    except Exception as e:
        print(f"  ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_double_roll_influence():
    """Test de l'influence des roulements sur la prédiction."""
    print("\nTest influence des roulements...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        analyzer = KenoDataAnalyzer()
        predictor = KenoPredictor(analyzer)
        predictor.initialize_weights()
        
        # Effectuer plusieurs roulements pour analyser l'influence
        print("  Effectuation de plusieurs roulements...")
        
        # Premier set de roulements
        predictor.roll_history = []  # Reset
        roll1 = predictor.perform_chance_roll('balanced')
        roll2 = predictor.perform_chance_roll('hot_numbers')
        
        influence1 = predictor.calculate_double_roll_influence()
        
        print(f"    Premier set - Roulement 1: {roll1}")
        print(f"    Premier set - Roulement 2: {roll2}")
        print(f"    Influence calculee: {len(influence1)} numeros")
        
        # Analyser les types d'influence
        high_influence = {k: v for k, v in influence1.items() if v >= 3.0}
        medium_influence = {k: v for k, v in influence1.items() if 2.0 <= v < 3.0}
        low_influence = {k: v for k, v in influence1.items() if 1.0 < v < 2.0}
        
        print(f"    Influence elevee (3.0+): {len(high_influence)} numeros")
        if high_influence:
            print(f"      Numeros: {sorted(high_influence.keys())}")
        
        print(f"    Influence moyenne (2.0-3.0): {len(medium_influence)} numeros")
        print(f"    Influence faible (1.0-2.0): {len(low_influence)} numeros")
        
        return True
        
    except Exception as e:
        print(f"  ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_double_roll_methods():
    """Test avec différentes méthodes de prédiction."""
    print("\nTest avec differentes methodes...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        analyzer = KenoDataAnalyzer()
        predictor = KenoPredictor(analyzer)
        predictor.initialize_weights()
        
        methods_to_test = ['balanced', 'weighted_random', 'hot_numbers']
        
        for method in methods_to_test:
            print(f"  Test methode: {method}")
            
            try:
                predictions, roll_details = predictor.predict_with_double_roll(5, method, 1)
                
                print(f"    Prediction: {sorted(predictions[0])}")
                print(f"    Roulements: {roll_details['total_rolls']}")
                
                # Analyser les roulements pour cette méthode
                rolls = [roll['numbers'] for roll in roll_details['roll_history']]
                if len(rolls) >= 2:
                    common = set(rolls[-2]) & set(rolls[-1])
                    print(f"    Numeros communs aux 2 roulements: {sorted(common) if common else 'Aucun'}")
                
            except Exception as e:
                print(f"    ERREUR avec {method}: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ERREUR: {e}")
        return False

def test_double_roll_consistency():
    """Test de cohérence du double roulement."""
    print("\nTest coherence du double roulement...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        analyzer = KenoDataAnalyzer()
        predictor = KenoPredictor(analyzer)
        predictor.initialize_weights()
        
        # Générer plusieurs prédictions et vérifier la cohérence
        print("  Generation de 5 predictions pour analyser la coherence...")
        
        all_predictions = []
        all_roll_details = []
        
        for i in range(5):
            predictions, roll_details = predictor.predict_with_double_roll(7, 'balanced', 1)
            all_predictions.extend(predictions)
            all_roll_details.append(roll_details)
            
            print(f"    Serie {i+1}: {sorted(predictions[0])} (roulements: {roll_details['total_rolls']})")
        
        # Analyser la diversité des prédictions
        unique_numbers = set()
        for pred in all_predictions:
            unique_numbers.update(pred)
        
        print(f"  Analyse coherence:")
        print(f"    Total predictions: {len(all_predictions)}")
        print(f"    Numeros uniques utilises: {len(unique_numbers)}/70")
        print(f"    Couverture: {len(unique_numbers)/70*100:.1f}%")
        
        # Vérifier que les roulements influencent bien les prédictions
        total_rolls = sum(details['total_rolls'] for details in all_roll_details)
        print(f"    Total roulements effectues: {total_rolls}")
        print(f"    Moyenne roulements par prediction: {total_rolls/len(all_predictions):.1f}")
        
        return True
        
    except Exception as e:
        print(f"  ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test principal."""
    print("TEST DU SYSTEME DOUBLE ROULEMENT CHANCE + CHANCE")
    print("=" * 60)
    
    tests = [
        ("Double roulement basique", test_double_roll_basic),
        ("Prediction avec double roulement", test_double_roll_prediction),
        ("Influence des roulements", test_double_roll_influence),
        ("Differentes methodes", test_double_roll_methods),
        ("Coherence du systeme", test_double_roll_consistency)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        try:
            success = test_func()
            results.append((name, success))
        except Exception as e:
            print(f"ERREUR dans {name}: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 60)
    print("RESULTATS:")
    
    all_good = True
    for name, success in results:
        status = "OK" if success else "ECHEC"
        print(f"  {name}: {status}")
        if not success:
            all_good = False
    
    print("\n" + "=" * 60)
    if all_good:
        print("SUCCES: Le systeme de double roulement fonctionne!")
        print("Fonctionnalites validees:")
        print("  - Activation/desactivation du double roulement")
        print("  - Roulements chance avec differentes methodes")
        print("  - Calcul d'influence des roulements")
        print("  - Predictions influencees par les roulements")
        print("  - Historique detaille des roulements")
        print("  - Coherence et diversite des predictions")
    else:
        print("ATTENTION: Des problemes ont ete detectes")
        print("Verifiez les erreurs ci-dessus.")
    
    return all_good

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)