"""
Script de test complet pour le système de prédiction Keno amélioré.
Démontre toutes les nouvelles fonctionnalités et améliorations.
"""

import sys
import logging
from datetime import datetime
from pathlib import Path

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_performance_analyzer():
    """Test de l'analyseur de performance."""
    print("\n" + "="*60)
    print("🔍 TEST ANALYSEUR DE PERFORMANCE")
    print("="*60)
    
    try:
        from performance_analyzer import KenoPerformanceAnalyzer
        
        analyzer = KenoPerformanceAnalyzer()
        report = analyzer.print_performance_summary()
        
        print("✅ Analyseur de performance testé avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test analyseur: {e}")
        return False

def test_enhanced_ml_model():
    """Test du modèle ML amélioré."""
    print("\n" + "="*60)
    print("🤖 TEST MODÈLE ML AMÉLIORÉ")
    print("="*60)
    
    try:
        from enhanced_ml_model import EnhancedKenoMLModel
        import pandas as pd
        import numpy as np
        
        # Créer des données de test
        test_data = []
        np.random.seed(42)
        for i in range(300):
            numbers = sorted(np.random.choice(range(1, 71), size=20, replace=False))
            test_data.append({
                'numbers': numbers,
                'date': pd.Timestamp('2023-01-01') + pd.Timedelta(days=i)
            })
        
        test_df = pd.DataFrame(test_data)
        
        # Tester le modèle
        model = EnhancedKenoMLModel()
        
        print("Entraînement du modèle amélioré...")
        metrics = model.train_models(test_df, max_samples=200)
        
        print(f"✅ Modèles entraînés: {metrics['models_trained']}/70")
        print(f"📊 AUC moyenne: {metrics['average_auc']:.3f}")
        print(f"🎯 Précision moyenne: {metrics['average_accuracy']:.3f}")
        
        # Test de prédiction
        predictions = model.predict_numbers(test_df, len(test_df)-1, 7)
        print(f"🔮 Prédiction test: {predictions}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test modèle ML amélioré: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adaptive_predictor():
    """Test du prédicteur adaptatif."""
    print("\n" + "="*60)
    print("🧠 TEST PRÉDICTEUR ADAPTATIF")
    print("="*60)
    
    try:
        from adaptive_predictor import AdaptiveKenoPredictor
        from data_analyzer import KenoDataAnalyzer
        
        # Initialiser l'analyseur avec des données
        analyzer = KenoDataAnalyzer()
        if not analyzer.load_processed_data():
            print("⚠️ Pas de données disponibles, création de données de test...")
            # Créer des données de test si nécessaire
            import pandas as pd
            import numpy as np
            
            test_data = []
            np.random.seed(42)
            for i in range(200):
                numbers = sorted(np.random.choice(range(1, 71), size=20, replace=False))
                test_data.append({
                    'numbers': numbers,
                    'date': pd.Timestamp('2023-01-01') + pd.Timedelta(days=i)
                })
            
            analyzer.data = pd.DataFrame(test_data)
        
        # Tester le prédicteur adaptatif
        predictor = AdaptiveKenoPredictor()
        predictor.analyzer = analyzer
        
        result = predictor.predict_adaptive(7)
        
        print(f"🔮 Prédictions: {result['predictions']}")
        print(f"🎯 Stratégie: {result['strategy']['name']}")
        print(f"📊 Confiance: {result['confidence']:.1%}")
        print(f"🔍 Patterns détectés: {result['patterns_detected']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test prédicteur adaptatif: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_advanced_backtest():
    """Test du moteur de backtest avancé."""
    print("\n" + "="*60)
    print("📊 TEST BACKTEST AVANCÉ")
    print("="*60)
    
    try:
        from advanced_backtest_engine import AdvancedBacktestEngine
        import pandas as pd
        import numpy as np
        
        # Créer des données de test
        test_data = []
        np.random.seed(42)
        for i in range(300):
            numbers = sorted(np.random.choice(range(1, 71), size=20, replace=False))
            test_data.append({
                'numbers': numbers,
                'date': pd.Timestamp('2023-01-01') + pd.Timedelta(days=i)
            })
        
        test_df = pd.DataFrame(test_data)
        
        engine = AdvancedBacktestEngine()
        
        print(f"✅ Moteur de backtest avancé créé")
        print(f"📋 Configuration:")
        for key, value in engine.config.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test backtest avancé: {e}")
        return False

def test_integrated_system():
    """Test du système intégré complet."""
    print("\n" + "="*60)
    print("🚀 TEST SYSTÈME INTÉGRÉ")
    print("="*60)
    
    try:
        from keno_predictor import KenoPredictor
        from data_analyzer import KenoDataAnalyzer
        
        # Initialiser le système
        analyzer = KenoDataAnalyzer()
        predictor = KenoPredictor(analyzer)
        
        print("🔧 Initialisation du système...")
        
        # Vérifier les méthodes disponibles
        methods = list(predictor.prediction_methods.keys())
        print(f"📋 Méthodes disponibles: {methods}")
        
        # Test des nouvelles méthodes
        new_methods = ['enhanced_ml', 'adaptive']
        available_new_methods = [m for m in new_methods if m in methods]
        
        if available_new_methods:
            print(f"✅ Nouvelles méthodes disponibles: {available_new_methods}")
            
            # Tester une prédiction avec une nouvelle méthode
            try:
                method = available_new_methods[0]
                print(f"🔮 Test prédiction avec {method}...")
                
                # Créer des données de test si nécessaire
                if analyzer.data is None:
                    import pandas as pd
                    import numpy as np
                    
                    test_data = []
                    np.random.seed(42)
                    for i in range(100):
                        numbers = sorted(np.random.choice(range(1, 71), size=20, replace=False))
                        test_data.append({
                            'numbers': numbers,
                            'date': pd.Timestamp('2023-01-01') + pd.Timedelta(days=i)
                        })
                    
                    analyzer.data = pd.DataFrame(test_data)
                
                predictions = predictor.predict(7, method=method)
                print(f"✅ Prédictions {method}: {predictions}")
                
            except Exception as e:
                print(f"⚠️ Erreur test prédiction {method}: {e}")
        else:
            print("⚠️ Aucune nouvelle méthode disponible")
        
        # Test des fonctionnalités avancées
        print("\n🔧 Test des fonctionnalités avancées...")
        
        # Test backtest avancé
        try:
            backtest_result = predictor.run_advanced_backtest(['balanced'], test_size=20)
            if 'error' not in backtest_result:
                print(f"✅ Backtest avancé: {backtest_result.get('best_method', 'N/A')}")
            else:
                print(f"⚠️ Backtest avancé: {backtest_result['error']}")
        except Exception as e:
            print(f"⚠️ Erreur backtest avancé: {e}")
        
        # Test auto-optimisation
        try:
            optimization_result = predictor.auto_optimize_system(optimization_cycles=1)
            if 'error' not in optimization_result:
                print(f"✅ Auto-optimisation: {optimization_result['cycles_completed']} cycles")
            else:
                print(f"⚠️ Auto-optimisation: {optimization_result['error']}")
        except Exception as e:
            print(f"⚠️ Erreur auto-optimisation: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test système intégré: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """Exécute tous les tests du système amélioré."""
    print("🧪 TESTS COMPLETS DU SYSTÈME KENO AMÉLIORÉ")
    print("=" * 80)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    tests = [
        ("Analyseur de Performance", test_performance_analyzer),
        ("Modèle ML Amélioré", test_enhanced_ml_model),
        ("Prédicteur Adaptatif", test_adaptive_predictor),
        ("Backtest Avancé", test_advanced_backtest),
        ("Système Intégré", test_integrated_system)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 Exécution: {test_name}")
        try:
            success = test_func()
            results[test_name] = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        except Exception as e:
            results[test_name] = f"❌ ERREUR: {e}"
    
    # Résumé final
    print("\n" + "="*80)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*80)
    
    success_count = 0
    for test_name, result in results.items():
        print(f"{result} {test_name}")
        if "✅" in result:
            success_count += 1
    
    print(f"\n🎯 Résultat global: {success_count}/{len(tests)} tests réussis")
    
    if success_count == len(tests):
        print("🎉 TOUS LES TESTS SONT PASSÉS - SYSTÈME OPÉRATIONNEL!")
    elif success_count >= len(tests) * 0.7:
        print("⚠️ SYSTÈME PARTIELLEMENT OPÉRATIONNEL")
    else:
        print("🚨 SYSTÈME NÉCESSITE DES CORRECTIONS")
    
    print("\n💡 AMÉLIORATIONS APPORTÉES:")
    print("  • 🤖 Modèle ML amélioré avec ensemble sophistiqué")
    print("  • 🧠 Prédicteur adaptatif auto-optimisant")
    print("  • 📊 Moteur de backtest avancé avec métriques détaillées")
    print("  • 🔄 Système d'auto-optimisation automatique")
    print("  • 📈 Analyseur de performance complet")
    print("  • 🎯 Intégration transparente dans le système existant")
    
    return results

if __name__ == "__main__":
    try:
        results = run_comprehensive_test()
        
        # Sauvegarder les résultats
        import json
        results_file = Path("test_results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'results': results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📁 Résultats sauvegardés dans: {results_file}")
        
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n💥 Erreur critique: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
