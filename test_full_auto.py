#!/usr/bin/env python3
"""
Test du système Full Auto - Meilleur prédicteur automatique.
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire actuel au path
sys.path.insert(0, os.path.dirname(__file__))

def test_predictor_evaluation():
    """Test d'évaluation des performances des prédicteurs."""
    print("Test evaluation des predicteurs...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        # Créer un prédicteur avec des données réelles
        analyzer = KenoDataAnalyzer()
        # Charger les données si disponibles
        try:
            analyzer.load_all_data()
            print("  Donnees reelles chargees")
        except:
            print("  Utilisation des donnees par defaut")
        
        predictor = KenoPredictor(analyzer)
        
        # Initialiser avec des poids par défaut
        predictor.initialize_weights()
        
        print("  Prédicteur créé avec succès")
        
        # Test d'évaluation d'une méthode
        print("  Test évaluation méthode 'balanced'...")
        performance = predictor.evaluate_predictor_performance('balanced', sample_size=10)
        
        if performance and 'error' not in performance:
            print(f"    Score: {performance['score']:.1f}%")
            print(f"    Précision: {performance['accuracy']:.1f}%")
            print(f"    Diversité: {performance['diversity']:.1f}%")
            print(f"    Tests effectués: {performance['tests_performed']}")
        else:
            print(f"    Évaluation échouée: {performance.get('error', 'Erreur inconnue')}")
        
        return True
        
    except Exception as e:
        print(f"  ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_best_predictor_selection():
    """Test de sélection automatique du meilleur prédicteur."""
    print("\\nTest sélection meilleur prédicteur...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        analyzer = KenoDataAnalyzer()
        predictor = KenoPredictor(analyzer)
        predictor.initialize_weights()
        
        print("  Test find_best_predictor...")
        best_method = predictor.find_best_predictor()
        
        print(f"    Meilleur prédicteur sélectionné: {best_method}")
        print(f"    Description: {predictor.get_method_description(best_method)}")
        
        # Vérifier que la méthode est valide
        if best_method in predictor.prediction_methods:
            print("    ✓ Méthode valide trouvée")
        else:
            print("    ❌ Méthode invalide!")
            return False
        
        # Vérifier le cache
        if predictor.best_method == best_method:
            print("    ✓ Cache mis à jour correctement")
        else:
            print("    ❌ Problème de cache!")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_auto_prediction():
    """Test de prédiction Full Auto complète."""
    print("\\nTest prédiction Full Auto...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        analyzer = KenoDataAnalyzer()
        predictor = KenoPredictor(analyzer)
        predictor.initialize_weights()
        
        print("  Test predict_full_auto...")
        predictions, details = predictor.predict_full_auto(7, 2)
        
        print(f"    Prédictions générées: {len(predictions)} grilles")
        for i, pred in enumerate(predictions, 1):
            print(f"      Grille {i}: {sorted(pred)}")
        
        print(f"    Méthode sélectionnée: {details['selected_method']}")
        print(f"    Full Auto activé: {details['full_auto']}")
        print(f"    Total roulements: {details['total_rolls']}")
        
        # Vérifier les détails
        required_keys = ['selected_method', 'method_description', 'full_auto', 'total_rolls', 'roll_history']
        for key in required_keys:
            if key not in details:
                print(f"    ❌ Clé manquante: {key}")
                return False
        
        print("    ✓ Tous les détails présents")
        
        # Vérifier que les roulements ont bien eu lieu
        if details['total_rolls'] >= 2:
            print("    ✓ Double roulement effectué")
        else:
            print("    ❌ Double roulement non effectué!")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_auto_performance():
    """Test de performance et cohérence du Full Auto."""
    print("\\nTest performance Full Auto...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        analyzer = KenoDataAnalyzer()
        predictor = KenoPredictor(analyzer)
        predictor.initialize_weights()
        
        # Générer plusieurs prédictions Full Auto
        print("  Génération de 3 prédictions Full Auto...")
        
        all_predictions = []
        all_methods = []
        
        for i in range(3):
            predictions, details = predictor.predict_full_auto(7, 1)
            all_predictions.extend(predictions)
            all_methods.append(details['selected_method'])
            
            print(f"    Série {i+1}: {sorted(predictions[0])} (méthode: {details['selected_method']})")
        
        # Analyser la cohérence
        unique_methods = set(all_methods)
        print(f"  Méthodes utilisées: {list(unique_methods)}")
        
        # Vérifier la diversité des prédictions
        unique_numbers = set()
        for pred in all_predictions:
            unique_numbers.update(pred)
        
        print(f"  Analyse diversité:")
        print(f"    Prédictions totales: {len(all_predictions)}")
        print(f"    Numéros uniques: {len(unique_numbers)}/70")
        print(f"    Couverture: {len(unique_numbers)/70*100:.1f}%")
        
        # Vérifier que le cache fonctionne
        if len(unique_methods) <= 2:  # Devrait souvent réutiliser la même méthode (cache)
            print("    ✓ Cache semble fonctionner (méthodes cohérentes)")
        else:
            print("    ⚠️ Cache peut-être non optimal (méthodes très variées)")
        
        return True
        
    except Exception as e:
        print(f"  ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_auto_vs_normal():
    """Comparaison Full Auto vs prédiction normale."""
    print("\\nTest comparaison Full Auto vs Normal...")
    
    try:
        from data_analyzer import KenoDataAnalyzer
        from keno_predictor import KenoPredictor
        
        analyzer = KenoDataAnalyzer()
        predictor = KenoPredictor(analyzer)
        predictor.initialize_weights()
        
        # Prédiction normale
        print("  Prédiction normale (balanced)...")
        normal_predictions = predictor.predict(7, 'balanced', 1)
        normal_confidence = predictor.get_prediction_confidence(normal_predictions[0])
        
        print(f"    Normal: {sorted(normal_predictions[0])} (confiance: {normal_confidence:.1f}%)")
        
        # Prédiction Full Auto
        print("  Prédiction Full Auto...")
        auto_predictions, auto_details = predictor.predict_full_auto(7, 1)
        auto_confidence = predictor.get_prediction_confidence(auto_predictions[0])
        
        print(f"    Full Auto: {sorted(auto_predictions[0])} (confiance: {auto_confidence:.1f}%)")
        print(f"    Méthode choisie: {auto_details['selected_method']}")
        
        # Comparaison
        if auto_details['selected_method'] != 'balanced':
            print("    ✓ Full Auto a choisi une méthode différente")
        else:
            print("    ⚠️ Full Auto a choisi 'balanced' (peut être optimal)")
        
        if auto_details['total_rolls'] >= 2:
            print("    ✓ Double roulement appliqué en Full Auto")
        else:
            print("    ❌ Double roulement non appliqué!")
            return False
        
        print(f"    Différence confiance: {auto_confidence - normal_confidence:+.1f}%")
        
        return True
        
    except Exception as e:
        print(f"  ERREUR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test principal."""
    print("TEST DU SYSTÈME FULL AUTO")
    print("=" * 50)
    
    tests = [
        ("Évaluation des prédicteurs", test_predictor_evaluation),
        ("Sélection meilleur prédicteur", test_best_predictor_selection),
        ("Prédiction Full Auto", test_full_auto_prediction),
        ("Performance Full Auto", test_full_auto_performance),
        ("Comparaison Full Auto vs Normal", test_full_auto_vs_normal)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\\n--- {name} ---")
        try:
            success = test_func()
            results.append((name, success))
        except Exception as e:
            print(f"ERREUR dans {name}: {e}")
            results.append((name, False))
    
    print("\\n" + "=" * 50)
    print("RÉSULTATS:")
    
    all_good = True
    for name, success in results:
        status = "✓ OK" if success else "❌ ÉCHEC"
        print(f"  {name}: {status}")
        if not success:
            all_good = False
    
    print("\\n" + "=" * 50)
    if all_good:
        print("🚀 SUCCÈS: Le système Full Auto fonctionne parfaitement!")
        print("Fonctionnalités validées:")
        print("  - Évaluation automatique des prédicteurs")
        print("  - Sélection du meilleur prédicteur")
        print("  - Prédiction Full Auto avec double roulement")
        print("  - Cache intelligent pour les performances")
        print("  - Interface complète avec détails")
    else:
        print("⚠️ ATTENTION: Des problèmes ont été détectés")
        print("Vérifiez les erreurs ci-dessus.")
    
    return all_good

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)