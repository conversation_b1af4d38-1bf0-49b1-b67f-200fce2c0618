import sys
import os
import json
from pathlib import Path

# Vérification de la présence et complétude des données Keno
# Usage: python data_verifier.py [--strict]

def main(strict=False):
    report = {
        "status": "unknown",
        "data_dir": "keno_data",
        "csv_files": [],
        "total_csv_files": 0,
        "required_sets": [
            "keno_1993_2013",
            "keno_2013_2018",
            "keno_2018_2020",
            "keno_2020_2025",
        ],
        "present_sets": [],
        "missing_sets": [],
        "has_cache": False,
        "cache_file": "keno_data/cache/combined_keno_data.pkl",
        "cache_rows": None,
        "issues": [],
    }

    base = Path("keno_data")
    if not base.exists():
        report["status"] = "missing_data_dir"
        report["issues"].append("Le répertoire keno_data est absent.")
        print(json.dumps(report, ensure_ascii=False, indent=2))
        return 2

    # Vérifier présence des dossiers attendus
    for d in report["required_sets"]:
        p = base / d
        if p.exists() and any(p.iterdir()):
            report["present_sets"].append(d)
        else:
            report["missing_sets"].append(d)

    # Lister CSV
    for root, _, files in os.walk(base):
        for f in files:
            if f.lower().endswith(".csv"):
                report["csv_files"].append(os.path.join(root, f))
    report["total_csv_files"] = len(report["csv_files"])

    # Vérifier le cache combiné si présent
    cache_path = Path(report["cache_file"]) 
    if cache_path.exists():
        report["has_cache"] = True
        try:
            import pickle
            with open(cache_path, "rb") as fh:
                payload = pickle.load(fh)
            if isinstance(payload, dict) and "total_rows" in payload:
                report["cache_rows"] = int(payload["total_rows"])  # valeur enregistrée par le downloader
            elif isinstance(payload, dict) and "data" in payload:
                # fallback: compter les lignes si data est un DataFrame
                try:
                    import pandas as pd  # lazy import local
                    report["cache_rows"] = int(len(payload["data"]))
                except Exception:
                    report["cache_rows"] = None
        except Exception as e:
            report["issues"].append(f"Cache illisible: {e}")

    # Optionnel: vérification approfondie via downloader/analyzer si disponibles
    try:
        from data_downloader import KenoDataDownloader
        dl = KenoDataDownloader()
        csvs = dl.get_all_csv_files()
        if csvs and len(csvs) > 0 and report["total_csv_files"] == 0:
            report["csv_files"] = csvs
            report["total_csv_files"] = len(csvs)
    except Exception:
        pass

    # Statut final
    ok_sets = len(report["missing_sets"]) == 0
    ok_csv = report["total_csv_files"] > 0
    ok_cache = report["has_cache"] and (report["cache_rows"] is None or report["cache_rows"] > 0)

    if ok_sets and ok_csv:
        report["status"] = "ok"
    elif ok_cache and ok_csv:
        report["status"] = "ok_with_cache"
    else:
        report["status"] = "incomplete"
        if not ok_sets:
            report["issues"].append("Certains sous-dossiers de données historiques sont manquants.")
        if not ok_csv:
            report["issues"].append("Aucun fichier CSV trouvé dans keno_data.")

    print(json.dumps(report, ensure_ascii=False, indent=2))
    return 0 if report["status"].startswith("ok") else (1 if not strict else 2)


if __name__ == "__main__":
    strict = "--strict" in sys.argv
    sys.exit(main(strict=strict))

