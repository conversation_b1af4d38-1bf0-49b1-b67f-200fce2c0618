import logging
import requests
import zipfile
import os
import json
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any

logger = logging.getLogger(__name__)

class SmartDataUpdater:
    """Mise à jour intelligente des données Keno depuis les sources FDJ.
    Utilise l'API FDJ qui fonctionne au lieu des URLs CSV obsolètes.
    """
    def __init__(self, data_dir="keno_data") -> None:
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # URL de l'API FDJ qui fonctionne
        self.api_url = "https://www.sto.api.fdj.fr/anonymous/service-draw-info/v3/documentations/1a2b3c4d-9876-4562-b3fc-2c963f66aft6"
        
        # Fichier de cache pour éviter les téléchargements redondants
        self.cache_file = self.data_dir / "last_api_download.json"

    def find_and_download_latest_file(self) -> Dict[str, Any]:
        """
        Télécharge les dernières données depuis l'API FDJ.
        
        Returns:
            Dict contenant le statut et les données téléchargées
        """
        try:
            logger.info("[UPDATER] Recherche des dernières données via API FDJ...")
            
            # Vérifier si on a déjà téléchargé récemment
            if self._check_recent_download():
                logger.info("[UPDATER] Données récentes déjà téléchargées")
                return {
                    "success": True,
                    "final_data": self._load_cached_analysis(),
                    "message": "Données récentes déjà disponibles"
                }
            
            # Télécharger depuis l'API
            response = requests.get(
                self.api_url,
                timeout=30,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'application/json, application/zip, */*',
                    'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8'
                }
            )
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '').lower()
                
                if 'application/zip' in content_type or 'application/octet-stream' in content_type:
                    return self._process_zip_download(response.content)
                elif 'application/json' in content_type:
                    return self._process_json_download(response.json())
                else:
                    logger.warning(f"[UPDATER] Type de contenu inattendu: {content_type}")
                    return {"success": False, "final_data": None, "error": "Type de contenu non supporté"}
            else:
                logger.error(f"[UPDATER] Erreur API: HTTP {response.status_code}")
                return {"success": False, "final_data": None, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"[UPDATER] Erreur téléchargement: {e}")
            return {"success": False, "final_data": None, "error": str(e)}
    
    def _check_recent_download(self) -> bool:
        """Vérifie si un téléchargement récent a déjà été effectué."""
        if not self.cache_file.exists():
            return False
            
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            last_download = datetime.fromisoformat(cache_data.get('timestamp', '1970-01-01'))
            # Considérer comme récent si téléchargé dans les 2 dernières heures
            return (datetime.now() - last_download).total_seconds() < 7200
            
        except Exception:
            return False
    
    def _process_zip_download(self, zip_content: bytes) -> Dict[str, Any]:
        """Traite un téléchargement ZIP depuis l'API."""
        try:
            # Sauvegarder le ZIP
            zip_path = self.data_dir / "api_latest_data.zip"
            with open(zip_path, 'wb') as f:
                f.write(zip_content)
            
            # Extraire le contenu
            extract_dir = self.data_dir / "api_latest"
            if extract_dir.exists():
                import shutil
                shutil.rmtree(extract_dir)
            extract_dir.mkdir(exist_ok=True)
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # Analyser les fichiers extraits
            analysis = self._analyze_extracted_files(extract_dir)
            
            # Mettre à jour le cache
            self._update_cache(analysis)
            
            logger.info(f"[UPDATER] ZIP téléchargé et extrait: {len(analysis.get('files', []))} fichiers")
            
            return {
                "success": True,
                "final_data": {"data_summary": analysis},
                "message": f"Nouvelles données téléchargées: {len(analysis.get('files', []))} fichiers"
            }
            
        except Exception as e:
            logger.error(f"[UPDATER] Erreur traitement ZIP: {e}")
            return {"success": False, "final_data": None, "error": str(e)}
    
    def _process_json_download(self, json_data: dict) -> Dict[str, Any]:
        """Traite un téléchargement JSON depuis l'API."""
        try:
            # Sauvegarder les données JSON
            json_path = self.data_dir / "api_latest_data.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            
            # Analyser les données JSON
            analysis = self._analyze_json_data(json_data)
            
            # Mettre à jour le cache
            self._update_cache(analysis)
            
            logger.info("[UPDATER] Données JSON téléchargées et analysées")
            
            return {
                "success": True,
                "final_data": {"data_summary": analysis},
                "message": "Nouvelles données JSON téléchargées"
            }
            
        except Exception as e:
            logger.error(f"[UPDATER] Erreur traitement JSON: {e}")
            return {"success": False, "final_data": None, "error": str(e)}
    
    def _analyze_extracted_files(self, extract_dir: Path) -> dict:
        """Analyse les fichiers extraits du ZIP."""
        analysis = {
            "timestamp": datetime.now().isoformat(),
            "files": [],
            "total_files": 0,
            "csv_files": 0,
            "has_recent_data": False
        }
        
        try:
            for file_path in extract_dir.rglob("*"):
                if file_path.is_file():
                    file_info = {
                        "name": file_path.name,
                        "size": file_path.stat().st_size,
                        "path": str(file_path.relative_to(extract_dir))
                    }
                    
                    # Vérifier si c'est un fichier CSV de données récentes
                    if file_path.suffix.lower() == '.csv':
                        analysis["csv_files"] += 1
                        file_info["type"] = "csv"
                        
                        # Vérifier si le fichier contient des données récentes
                        if self._check_recent_data_in_file(file_path):
                            analysis["has_recent_data"] = True
                            file_info["has_recent_data"] = True
                    
                    analysis["files"].append(file_info)
                    analysis["total_files"] += 1
                    
        except Exception as e:
            logger.warning(f"[UPDATER] Erreur analyse fichiers: {e}")
        
        return analysis
    
    def _analyze_json_data(self, json_data: dict) -> dict:
        """Analyse les données JSON de l'API."""
        analysis = {
            "timestamp": datetime.now().isoformat(),
            "type": "json",
            "has_recent_data": False,
            "data_keys": list(json_data.keys()) if isinstance(json_data, dict) else []
        }
        
        # Rechercher des indicateurs de données récentes
        if isinstance(json_data, dict):
            # Chercher des dates récentes ou des tirages récents
            for key, value in json_data.items():
                if any(date_key in key.lower() for date_key in ['date', 'tirage', 'draw', 'time']):
                    analysis["has_recent_data"] = True
                    break
        
        return analysis
    
    def _check_recent_data_in_file(self, file_path: Path) -> bool:
        """Vérifie si un fichier CSV contient des données récentes."""
        try:
            # Lire les premières lignes pour détecter des dates récentes
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(1000)  # Lire les premiers 1000 caractères
                
            # Chercher des dates récentes (derniers 30 jours)
            current_date = datetime.now()
            for days_back in range(30):
                check_date = current_date - timedelta(days=days_back)
                date_patterns = [
                    check_date.strftime("%Y-%m-%d"),
                    check_date.strftime("%d/%m/%Y"),
                    check_date.strftime("%Y%m%d")
                ]
                
                for pattern in date_patterns:
                    if pattern in content:
                        return True
                        
        except Exception:
            pass
            
        return False
    
    def _update_cache(self, analysis: dict):
        """Met à jour le cache des téléchargements."""
        try:
            cache_data = {
                "timestamp": datetime.now().isoformat(),
                "last_analysis": analysis
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.warning(f"[UPDATER] Erreur mise à jour cache: {e}")
    
    def _load_cached_analysis(self) -> dict:
        """Charge l'analyse mise en cache."""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            return {"data_summary": cache_data.get("last_analysis", {})}
        except Exception:
            return {}

