#!/usr/bin/env python3
"""
Test des téléchargements de données Keno actuels.
Vérifie si les URLs et méthodes récupèrent bien les dernières données.
"""

import requests
from datetime import datetime, timedelta
import sys
import os

# Ajouter le répertoire actuel au path
sys.path.insert(0, os.path.dirname(__file__))

def test_fdj_urls():
    """Test des URLs FDJ pour vérifier leur disponibilité."""
    print("Test URLs FDJ officielles...")
    
    # URLs de base
    base_urls = {
        "csv_historical": "https://cdn-media.fdj.fr/static-draws/csv/keno/",
        "keno_page": "https://www.fdj.fr/jeux-de-tirage/keno/historique",
        "api_latest": "https://www.sto.api.fdj.fr/anonymous/service-draw-info/v3/documentations/1a2b3c4d-9876-4562-b3fc-2c963f66aft6"
    }
    
    results = {}
    
    for name, url in base_urls.items():
        try:
            print(f"  Test {name}: {url}")
            response = requests.get(url, timeout=10, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            status = response.status_code
            content_type = response.headers.get('content-type', 'unknown')
            content_length = len(response.content)
            
            print(f"    Status: {status}")
            print(f"    Content-Type: {content_type}")
            print(f"    Taille: {content_length} bytes")
            
            results[name] = {
                'status': status,
                'accessible': status == 200,
                'content_type': content_type,
                'size': content_length
            }
            
        except Exception as e:
            print(f"    ERREUR: {e}")
            results[name] = {
                'status': 'error',
                'accessible': False,
                'error': str(e)
            }
    
    return results

def test_current_month_file():
    """Test de téléchargement du fichier du mois actuel."""
    print("\nTest fichier mois actuel...")
    
    current_date = datetime.now()
    year = current_date.year
    month = current_date.month
    
    # Test mois actuel
    filename = f"keno_{year}{month:02d}.zip"
    url = f"https://cdn-media.fdj.fr/static-draws/csv/keno/{filename}"
    
    print(f"  URL: {url}")
    
    try:
        response = requests.head(url, timeout=10, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        status = response.status_code
        last_modified = response.headers.get('last-modified', 'unknown')
        content_length = response.headers.get('content-length', 'unknown')
        
        print(f"    Status: {status}")
        print(f"    Derniere modification: {last_modified}")
        print(f"    Taille: {content_length} bytes")
        
        if status == 200:
            print("    FICHIER DISPONIBLE - Donnees du mois actuel accessibles")
            return True
        else:
            print(f"    FICHIER NON DISPONIBLE (HTTP {status})")
            return False
            
    except Exception as e:
        print(f"    ERREUR: {e}")
        return False

def test_previous_month_file():
    """Test de téléchargement du fichier du mois précédent."""
    print("\nTest fichier mois precedent...")
    
    # Mois précédent
    current_date = datetime.now()
    prev_date = current_date.replace(day=1) - timedelta(days=1)
    year = prev_date.year
    month = prev_date.month
    
    filename = f"keno_{year}{month:02d}.zip"
    url = f"https://cdn-media.fdj.fr/static-draws/csv/keno/{filename}"
    
    print(f"  URL: {url}")
    
    try:
        response = requests.head(url, timeout=10, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        status = response.status_code
        last_modified = response.headers.get('last-modified', 'unknown')
        content_length = response.headers.get('content-length', 'unknown')
        
        print(f"    Status: {status}")
        print(f"    Derniere modification: {last_modified}")
        print(f"    Taille: {content_length} bytes")
        
        if status == 200:
            print("    FICHIER DISPONIBLE - Donnees du mois precedent accessibles")
            return True
        else:
            print(f"    FICHIER NON DISPONIBLE (HTTP {status})")
            return False
            
    except Exception as e:
        print(f"    ERREUR: {e}")
        return False

def test_realtime_functionality():
    """Test des fonctionnalités temps réel."""
    print("\nTest fonctionnalites temps reel...")
    
    try:
        from realtime_keno_fetcher import RealtimeKenoFetcher
        
        fetcher = RealtimeKenoFetcher()
        
        # Test vérification données manquantes
        print("  Test verification donnees manquantes...")
        missing_report = fetcher.check_missing_recent_data()
        
        print(f"    Tirages attendus: {missing_report.get('total_expected', 'unknown')}")
        print(f"    Tirages trouves: {missing_report.get('total_found', 'unknown')}")
        print(f"    Tirages manquants: {len(missing_report.get('missing_tirages', []))}")
        
        # Test mise à jour si nécessaire
        print("  Test mise a jour automatique...")
        update_report = fetcher.update_data_if_needed()
        
        print(f"    Mise a jour necessaire: {update_report.get('update_needed', False)}")
        print(f"    Nombre manquant: {update_report.get('missing_count', 0)}")
        print(f"    Succes: {update_report.get('success', False)}")
        
        return True
        
    except Exception as e:
        print(f"    ERREUR: {e}")
        return False

def test_smart_updater():
    """Test du smart updater."""
    print("\nTest Smart Data Updater...")
    
    try:
        from smart_data_updater import SmartDataUpdater
        
        updater = SmartDataUpdater()
        result = updater.find_and_download_latest_file()
        
        print(f"    Succes: {result.get('success', False)}")
        print(f"    Donnees finales: {result.get('final_data', 'None')}")
        
        # Note: Le SmartDataUpdater actuel est un stub
        print("    NOTE: SmartDataUpdater est actuellement un stub - ne telecharge pas reellement")
        
        return True
        
    except Exception as e:
        print(f"    ERREUR: {e}")
        return False

def main():
    """Test principal."""
    print("TEST DES TÉLÉCHARGEMENTS DE DONNEES KENO")
    print("=" * 50)
    print(f"Date du test: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("URLs FDJ", test_fdj_urls),
        ("Fichier mois actuel", test_current_month_file),
        ("Fichier mois precedent", test_previous_month_file),
        ("Fonctionnalites temps reel", test_realtime_functionality),
        ("Smart Updater", test_smart_updater)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        try:
            success = test_func()
            results.append((name, success))
        except Exception as e:
            print(f"ERREUR dans {name}: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 50)
    print("RESUME DES TESTS:")
    
    all_good = True
    for name, success in results:
        status = "OK" if success else "PROBLEME"
        print(f"  {name}: {status}")
        if not success:
            all_good = False
    
    print("\n" + "=" * 50)
    if all_good:
        print("VERDICT: Les telecharements semblent fonctionnels")
        print("Les donnees les plus recentes devraient etre accessibles.")
    else:
        print("VERDICT: Des problemes ont ete detectes")
        print("Certaines sources de donnees peuvent ne pas etre a jour.")
        
    print(f"\nDate de verification: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return all_good

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)