#!/usr/bin/env python3
"""
Test spécifique pour vérifier si les téléchargements récupèrent les dernières données.
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Ajouter le répertoire actuel au path
sys.path.insert(0, os.path.dirname(__file__))

def test_smart_updater_download():
    """Test du téléchargement via SmartDataUpdater."""
    print("Test SmartDataUpdater - Téléchargement réel...")
    
    try:
        from smart_data_updater import SmartDataUpdater
        
        # Supprimer le cache pour forcer un nouveau téléchargement
        data_dir = Path("keno_data")
        cache_file = data_dir / "last_api_download.json"
        if cache_file.exists():
            cache_file.unlink()
            print("  Cache supprimé - forcera un nouveau téléchargement")
        
        updater = SmartDataUpdater()
        result = updater.find_and_download_latest_file()
        
        print(f"  Succès: {result.get('success', False)}")
        print(f"  Message: {result.get('message', 'N/A')}")
        
        if result.get('success'):
            final_data = result.get('final_data', {})
            data_summary = final_data.get('data_summary', {})
            
            print(f"  Fichiers trouvés: {data_summary.get('total_files', 0)}")
            print(f"  Fichiers CSV: {data_summary.get('csv_files', 0)}")
            print(f"  Données récentes: {data_summary.get('has_recent_data', False)}")
            
            # Afficher les détails des fichiers
            files = data_summary.get('files', [])
            for file_info in files:
                print(f"    - {file_info.get('name', 'Unknown')}: {file_info.get('size', 0)} bytes")
                if file_info.get('has_recent_data'):
                    print(f"      ✓ Contient des données récentes")
            
            return True
        else:
            print(f"  Erreur: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"  ERREUR: {e}")
        return False

def test_realtime_fetcher_api():
    """Test du téléchargement via RealtimeKenoFetcher API."""
    print("\nTest RealtimeKenoFetcher - API...")
    
    try:
        from realtime_keno_fetcher import RealtimeKenoFetcher
        
        fetcher = RealtimeKenoFetcher()
        result = fetcher.download_latest_from_api()
        
        print(f"  Succès: {result.get('success', False)}")
        print(f"  Source: {result.get('source', 'N/A')}")
        print(f"  Message: {result.get('message', 'N/A')}")
        
        if result.get('success'):
            files = result.get('files', [])
            print(f"  Fichiers extraits: {len(files)}")
            
            for file_path in files:
                print(f"    - {Path(file_path).name}")
            
            # Vérifier les données
            data = result.get('data')
            if data is not None:
                print(f"  Tirages dans les données: {len(data)}")
                
                # Vérifier les dates récentes
                if hasattr(data, 'columns') and 'date_de_tirage' in data.columns:
                    latest_date = data['date_de_tirage'].max()
                    print(f"  Date la plus récente: {latest_date}")
                    
                    # Vérifier si c'est récent (derniers 7 jours)
                    try:
                        from datetime import datetime
                        if isinstance(latest_date, str):
                            latest_date = datetime.strptime(latest_date, '%Y-%m-%d')
                        
                        days_ago = (datetime.now() - latest_date).days
                        print(f"  Ancienneté: {days_ago} jours")
                        
                        if days_ago <= 7:
                            print("  ✓ Données très récentes (≤ 7 jours)")
                            return True
                        elif days_ago <= 30:
                            print("  ~ Données assez récentes (≤ 30 jours)")
                            return True
                        else:
                            print("  ⚠ Données anciennes (> 30 jours)")
                            return False
                            
                    except Exception as e:
                        print(f"  Erreur analyse dates: {e}")
            
            return True
        else:
            print(f"  Erreur: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"  ERREUR: {e}")
        return False

def check_downloaded_files():
    """Vérifie les fichiers téléchargés."""
    print("\nVérification des fichiers téléchargés...")
    
    data_dir = Path("keno_data")
    if not data_dir.exists():
        print("  Aucun répertoire de données trouvé")
        return False
    
    # Chercher les fichiers récents
    api_dir = data_dir / "api_latest"
    recent_files = []
    
    if api_dir.exists():
        csv_files = list(api_dir.glob("*.csv"))
        print(f"  Fichiers CSV de l'API: {len(csv_files)}")
        
        for csv_file in csv_files:
            try:
                # Lire les premières lignes pour analyser
                with open(csv_file, 'r', encoding='utf-8') as f:
                    first_lines = f.readlines()[:10]
                
                print(f"    - {csv_file.name}: {len(first_lines)} lignes d'en-tête")
                
                # Chercher des dates récentes dans les premières lignes
                current_date = datetime.now()
                for line in first_lines:
                    # Chercher des patterns de date récente
                    for days_back in range(30):
                        check_date = current_date - timedelta(days=days_back)
                        date_patterns = [
                            check_date.strftime("%Y-%m-%d"),
                            check_date.strftime("%d/%m/%Y"),
                            check_date.strftime("%Y%m%d")
                        ]
                        
                        for pattern in date_patterns:
                            if pattern in line:
                                recent_files.append(csv_file.name)
                                print(f"      ✓ Trouvé date récente: {pattern}")
                                break
                                
            except Exception as e:
                print(f"    - {csv_file.name}: Erreur lecture ({e})")
    
    print(f"  Fichiers avec données récentes: {len(recent_files)}")
    
    return len(recent_files) > 0

def main():
    """Test principal."""
    print("TEST TÉLÉCHARGEMENT DES DERNIÈRES DONNÉES KENO")
    print("=" * 60)
    print(f"Date du test: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("SmartDataUpdater", test_smart_updater_download),
        ("RealtimeKenoFetcher API", test_realtime_fetcher_api),
        ("Fichiers téléchargés", check_downloaded_files)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n--- {name} ---")
        try:
            success = test_func()
            results.append((name, success))
        except Exception as e:
            print(f"ERREUR dans {name}: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 60)
    print("RÉSULTATS:")
    
    all_good = True
    recent_data_available = False
    
    for name, success in results:
        status = "✓ OK" if success else "✗ PROBLÈME"
        print(f"  {name}: {status}")
        if not success:
            all_good = False
        if success and ("données récentes" in name.lower() or "api" in name.lower()):
            recent_data_available = True
    
    print("\n" + "=" * 60)
    if recent_data_available:
        print("✓ VERDICT: Les téléchargements récupèrent les dernières données")
        print("L'application peut accéder aux données Keno les plus récentes.")
        print("\nSources fonctionnelles:")
        print("  - API FDJ (priorité)")
        print("  - SmartDataUpdater avec cache intelligent")
        print("  - Fallback vers sources alternatives")
    elif all_good:
        print("~ VERDICT: Téléchargements fonctionnels mais données possiblement anciennes")
        print("Les mécanismes fonctionnent mais les données peuvent ne pas être les plus récentes.")
    else:
        print("✗ VERDICT: Problèmes de téléchargement détectés")
        print("Certains mécanismes ne fonctionnent pas correctement.")
        
    print(f"\nVérification effectuée le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return recent_data_available

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)