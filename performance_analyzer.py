"""
Analyseur de performance pour le système de prédiction Keno.
Évalue les métriques actuelles et identifie les points d'amélioration.
"""

import pickle
import json
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)

class KenoPerformanceAnalyzer:
    """Analyseur de performance pour le système Keno."""
    
    def __init__(self, cache_dir="keno_data/cache"):
        """Initialise l'analyseur de performance."""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Fichiers de métriques
        self.training_metrics_file = self.cache_dir / "training_metrics.pkl"
        self.backtest_results_file = self.cache_dir / "backtest_results.pkl"
        self.optimization_results_file = self.cache_dir / "optimization_results.pkl"
        
        # Données chargées
        self.training_metrics = None
        self.backtest_results = None
        self.optimization_results = None
        
    def load_existing_metrics(self) -> Dict:
        """Charge toutes les métriques existantes."""
        metrics = {
            'training_metrics': None,
            'backtest_results': None,
            'optimization_results': None,
            'load_status': {}
        }
        
        # Charger les métriques d'entraînement ML
        if self.training_metrics_file.exists():
            try:
                with open(self.training_metrics_file, 'rb') as f:
                    self.training_metrics = pickle.load(f)
                    metrics['training_metrics'] = self.training_metrics
                    metrics['load_status']['training_metrics'] = 'success'
                    logger.info(f"Métriques ML chargées: {len(self.training_metrics)} modèles")
            except Exception as e:
                logger.error(f"Erreur chargement métriques ML: {e}")
                metrics['load_status']['training_metrics'] = f'error: {e}'
        else:
            metrics['load_status']['training_metrics'] = 'file_not_found'
            
        # Charger les résultats de backtest
        if self.backtest_results_file.exists():
            try:
                with open(self.backtest_results_file, 'rb') as f:
                    self.backtest_results = pickle.load(f)
                    metrics['backtest_results'] = self.backtest_results
                    metrics['load_status']['backtest_results'] = 'success'
                    logger.info("Résultats de backtest chargés")
            except Exception as e:
                logger.error(f"Erreur chargement backtest: {e}")
                metrics['load_status']['backtest_results'] = f'error: {e}'
        else:
            metrics['load_status']['backtest_results'] = 'file_not_found'
            
        # Charger les résultats d'optimisation
        if self.optimization_results_file.exists():
            try:
                with open(self.optimization_results_file, 'rb') as f:
                    self.optimization_results = pickle.load(f)
                    metrics['optimization_results'] = self.optimization_results
                    metrics['load_status']['optimization_results'] = 'success'
                    logger.info("Résultats d'optimisation chargés")
            except Exception as e:
                logger.error(f"Erreur chargement optimisation: {e}")
                metrics['load_status']['optimization_results'] = f'error: {e}'
        else:
            metrics['load_status']['optimization_results'] = 'file_not_found'
            
        return metrics
    
    def analyze_ml_performance(self) -> Dict:
        """Analyse les performances des modèles ML."""
        if not self.training_metrics:
            return {'status': 'no_data', 'message': 'Aucune métrique ML disponible'}
        
        analysis = {
            'model_count': len(self.training_metrics),
            'performance_summary': {},
            'best_models': {},
            'worst_models': {},
            'average_metrics': {},
            'performance_distribution': {},
            'recommendations': []
        }
        
        # Extraire les métriques par numéro
        accuracies = []
        aucs = []
        positive_rates = []
        
        for num, metrics in self.training_metrics.items():
            if isinstance(metrics, dict):
                accuracies.append(metrics.get('accuracy', 0))
                aucs.append(metrics.get('auc', 0))
                positive_rates.append(metrics.get('positive_rate', 0))
        
        if accuracies:
            analysis['average_metrics'] = {
                'accuracy': np.mean(accuracies),
                'accuracy_std': np.std(accuracies),
                'auc': np.mean(aucs),
                'auc_std': np.std(aucs),
                'positive_rate': np.mean(positive_rates),
                'positive_rate_std': np.std(positive_rates)
            }
            
            # Identifier les meilleurs et pires modèles
            accuracy_scores = [(num, metrics.get('accuracy', 0)) 
                             for num, metrics in self.training_metrics.items() 
                             if isinstance(metrics, dict)]
            accuracy_scores.sort(key=lambda x: x[1], reverse=True)
            
            analysis['best_models'] = dict(accuracy_scores[:5])
            analysis['worst_models'] = dict(accuracy_scores[-5:])
            
            # Distribution des performances
            analysis['performance_distribution'] = {
                'accuracy_quartiles': np.percentile(accuracies, [25, 50, 75]).tolist(),
                'auc_quartiles': np.percentile(aucs, [25, 50, 75]).tolist(),
                'high_performers': len([a for a in accuracies if a > 0.6]),
                'low_performers': len([a for a in accuracies if a < 0.5])
            }
            
            # Recommandations basées sur l'analyse
            avg_accuracy = analysis['average_metrics']['accuracy']
            if avg_accuracy < 0.5:
                analysis['recommendations'].append("CRITIQUE: Précision moyenne très faible (<50%)")
            elif avg_accuracy < 0.6:
                analysis['recommendations'].append("ATTENTION: Précision moyenne faible (<60%)")
            
            if analysis['performance_distribution']['low_performers'] > len(accuracies) * 0.3:
                analysis['recommendations'].append("Trop de modèles sous-performants (>30%)")
                
            if np.std(accuracies) > 0.1:
                analysis['recommendations'].append("Forte variance entre modèles - optimisation nécessaire")
        
        return analysis
    
    def analyze_prediction_methods(self) -> Dict:
        """Analyse les performances des différentes méthodes de prédiction."""
        if not self.backtest_results:
            return {'status': 'no_data', 'message': 'Aucun résultat de backtest disponible'}
        
        analysis = {
            'methods_tested': [],
            'method_performance': {},
            'best_method': None,
            'performance_comparison': {},
            'recommendations': []
        }
        
        # Analyser les résultats de backtest
        if 'method_performance' in self.backtest_results:
            method_perf = self.backtest_results['method_performance']
            analysis['methods_tested'] = list(method_perf.keys())
            analysis['method_performance'] = method_perf
            
            # Identifier la meilleure méthode
            if method_perf:
                best_method = max(method_perf.items(), 
                                key=lambda x: x[1].get('overall_score', 0))
                analysis['best_method'] = {
                    'name': best_method[0],
                    'score': best_method[1].get('overall_score', 0),
                    'metrics': best_method[1]
                }
                
                # Comparaison des performances
                scores = {method: metrics.get('overall_score', 0) 
                         for method, metrics in method_perf.items()}
                analysis['performance_comparison'] = sorted(scores.items(), 
                                                          key=lambda x: x[1], 
                                                          reverse=True)
                
                # Recommandations
                best_score = analysis['best_method']['score']
                if best_score < 0.3:
                    analysis['recommendations'].append("CRITIQUE: Toutes les méthodes sous-performent (<30%)")
                elif best_score < 0.5:
                    analysis['recommendations'].append("ATTENTION: Meilleure méthode sous-optimale (<50%)")
                
                # Analyser la diversité des performances
                score_values = list(scores.values())
                if len(score_values) > 1:
                    score_range = max(score_values) - min(score_values)
                    if score_range < 0.1:
                        analysis['recommendations'].append("Faible différenciation entre méthodes")
        
        return analysis
    
    def generate_performance_report(self) -> Dict:
        """Génère un rapport complet de performance."""
        print("🔍 Analyse des performances du système Keno...")
        
        # Charger toutes les métriques
        metrics = self.load_existing_metrics()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'data_availability': metrics['load_status'],
            'ml_analysis': self.analyze_ml_performance(),
            'method_analysis': self.analyze_prediction_methods(),
            'overall_recommendations': [],
            'improvement_priorities': []
        }
        
        # Recommandations globales
        if report['ml_analysis'].get('status') != 'no_data':
            ml_recs = report['ml_analysis'].get('recommendations', [])
            report['overall_recommendations'].extend(ml_recs)
            
        if report['method_analysis'].get('status') != 'no_data':
            method_recs = report['method_analysis'].get('recommendations', [])
            report['overall_recommendations'].extend(method_recs)
        
        # Priorités d'amélioration
        if any('CRITIQUE' in rec for rec in report['overall_recommendations']):
            report['improvement_priorities'].append("URGENT: Refonte complète des modèles ML")
        
        if any('sous-performent' in rec for rec in report['overall_recommendations']):
            report['improvement_priorities'].append("Optimisation des algorithmes de prédiction")
            
        if any('variance' in rec for rec in report['overall_recommendations']):
            report['improvement_priorities'].append("Standardisation et calibration des modèles")
        
        return report
    
    def print_performance_summary(self):
        """Affiche un résumé des performances."""
        report = self.generate_performance_report()
        
        print("\n" + "="*60)
        print("📊 RAPPORT DE PERFORMANCE - SYSTÈME KENO")
        print("="*60)
        
        # État des données
        print("\n📁 DISPONIBILITÉ DES DONNÉES:")
        for data_type, status in report['data_availability'].items():
            status_icon = "✅" if status == 'success' else "❌" if 'error' in status else "⚠️"
            print(f"  {status_icon} {data_type}: {status}")
        
        # Analyse ML
        if report['ml_analysis'].get('status') != 'no_data':
            ml_analysis = report['ml_analysis']
            print(f"\n🤖 ANALYSE MACHINE LEARNING:")
            print(f"  📈 Modèles entraînés: {ml_analysis['model_count']}")
            
            if 'average_metrics' in ml_analysis:
                avg_metrics = ml_analysis['average_metrics']
                print(f"  🎯 Précision moyenne: {avg_metrics['accuracy']:.1%}")
                print(f"  📊 AUC moyenne: {avg_metrics['auc']:.3f}")
                print(f"  🔄 Taux positif moyen: {avg_metrics['positive_rate']:.1%}")
        
        # Analyse des méthodes
        if report['method_analysis'].get('status') != 'no_data':
            method_analysis = report['method_analysis']
            print(f"\n🎲 ANALYSE DES MÉTHODES:")
            print(f"  🧪 Méthodes testées: {len(method_analysis['methods_tested'])}")
            
            if method_analysis.get('best_method'):
                best = method_analysis['best_method']
                print(f"  🏆 Meilleure méthode: {best['name']}")
                print(f"  📊 Score: {best['score']:.1%}")
        
        # Recommandations
        if report['overall_recommendations']:
            print(f"\n⚠️ RECOMMANDATIONS:")
            for i, rec in enumerate(report['overall_recommendations'], 1):
                print(f"  {i}. {rec}")
        
        # Priorités
        if report['improvement_priorities']:
            print(f"\n🚀 PRIORITÉS D'AMÉLIORATION:")
            for i, priority in enumerate(report['improvement_priorities'], 1):
                print(f"  {i}. {priority}")
        
        print("\n" + "="*60)
        
        return report


if __name__ == "__main__":
    # Test de l'analyseur
    analyzer = KenoPerformanceAnalyzer()
    report = analyzer.print_performance_summary()
