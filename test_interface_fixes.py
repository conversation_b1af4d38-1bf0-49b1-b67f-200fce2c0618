#!/usr/bin/env python3
"""
Script de test pour valider les corrections d'interface.
"""

import tkinter as tk
import sys
import os

# Ajouter le répertoire actuel au path
sys.path.insert(0, os.path.dirname(__file__))

def test_interface_creation():
    """Test de création de l'interface."""
    try:
        print("Test creation de l'interface...")
        root = tk.Tk()
        root.withdraw()  # Masquer la fenêtre pour le test
        
        from enhanced_keno_gui import EnhancedKenoGUI
        app = EnhancedKenoGUI(root)
        
        print("Interface creee avec succes")
        
        # Test des méthodes de sécurité
        print("Test Test des méthodes de sécurité...")
        
        # Test vérification état
        is_ok, message = app._check_interface_state()
        print(f"   État interface: {is_ok} - {message}")
        
        # Test gestion barre de progression
        app._safe_progress_start()
        app._safe_progress_stop()
        print("   Gestion barre de progression: OK")
        
        # Test vérification prérequis
        has_prereq = app._check_prerequisites("test")
        print(f"   Vérification prérequis: {has_prereq}")
        
        print("OK Tous les tests de sécurité passés")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"ERREUR Erreur test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_button_protection():
    """Test de protection contre les clics multiples."""
    try:
        print("Test Test protection boutons...")
        root = tk.Tk()
        root.withdraw()
        
        from enhanced_keno_gui import EnhancedKenoGUI
        app = EnhancedKenoGUI(root)
        
        # Simuler un état de chargement
        app._loading_in_progress = True
        
        # Essayer de déclencher un chargement (doit être bloqué)
        print("   Test protection chargement multiple...")
        app.load_data_threaded()  # Ne devrait rien faire
        
        print("OK Protection contre clics multiples fonctionne")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"ERREUR Erreur test protection: {e}")
        return False

def test_error_handling():
    """Test de gestion d'erreurs."""
    try:
        print("Test Test gestion d'erreurs...")
        root = tk.Tk()
        root.withdraw()
        
        from enhanced_keno_gui import EnhancedKenoGUI
        app = EnhancedKenoGUI(root)
        
        # Test gestionnaire d'erreur global
        try:
            # Simuler une erreur Tkinter
            app._handle_tk_error(ValueError, ValueError("Test error"), None)
            print("   Gestionnaire d'erreur global: OK")
        except Exception:
            print("   Gestionnaire d'erreur global: Erreur")
            
        print("OK Gestion d'erreurs fonctionne")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"ERREUR Erreur test gestion d'erreurs: {e}")
        return False

def main():
    """Fonction principale de test."""
    print(" TESTS DES CORRECTIONS D'INTERFACE")
    print("=" * 40)
    
    tests = [
        ("Création interface", test_interface_creation),
        ("Protection boutons", test_button_protection), 
        ("Gestion erreurs", test_error_handling)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n- {name}...")
        success = test_func()
        results.append((name, success))
        
    print("\n" + "=" * 40)
    print("RESULTATS RÉSULTATS:")
    
    all_passed = True
    for name, success in results:
        status = "OK PASSÉ" if success else "ERREUR ÉCHOUÉ"
        print(f"   {name}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        print("\nSUCCES TOUS LES TESTS SONT PASSÉS!")
        print("Les corrections d'interface sont fonctionnelles.")
    else:
        print("\nATTENTION CERTAINS TESTS ONT ÉCHOUÉ")
        print("Vérifiez les erreurs ci-dessus.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)