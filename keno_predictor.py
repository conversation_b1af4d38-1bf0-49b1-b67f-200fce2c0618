"""
Algorithme de prédiction pour le jeu Keno basé sur l'analyse statistique des données historiques.
"""

import random
from collections import defaultdict
import logging
import pandas as pd
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
from data_analyzer import KenoDataAnalyzer

# Lazy import to avoid heavy numpy import at cold start
def _lazy_np():
    import numpy as np
    return np

# Import conditionnel du ML pour éviter les erreurs si CatBoost n'est pas installé
try:
    from ml_model import KenoMLModel
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    KenoMLModel = None

logger = logging.getLogger(__name__)

class KenoPredictor:
    """Classe principale pour la prédiction des numéros Keno."""
    
    def __init__(self, analyzer=None):
        """
        Initialise le prédicteur.

        Args:
            analyzer (KenoDataAnalyzer): Analyseur de données pré-configuré
        """
        self.analyzer = analyzer or KenoDataAnalyzer()
        self.weights = {}

        # Initialiser le modèle ML si disponible
        self.ml_model = None
        if ML_AVAILABLE:
            try:
                self.ml_model = KenoMLModel()
                logger.info("Modèle ML CatBoost initialisé")
            except Exception as e:
                logger.warning(f"Impossible d'initialiser le modèle ML: {e}")
                self.ml_model = None

        # Initialiser le prédicteur ultra si possible
        self.ultra_predictor = None
        try:
            from ultra_predictor import UltraKenoPredictor
            self.ultra_predictor = UltraKenoPredictor(self.analyzer)
            logger.info("Prédicteur ultra-optimisé initialisé")
        except Exception as e:
            logger.warning(f"Prédicteur ultra non disponible: {e}")

        # Méthodes de prédiction (incluant ML si disponible)
        self.prediction_methods = {
            'weighted_random': self._weighted_random_prediction,
            'hot_numbers': self._hot_numbers_prediction,
            'balanced': self._balanced_prediction,
            'pattern_based': self._pattern_based_prediction,
            'anti_pattern': self._anti_pattern_prediction
        }
        
        # Ajouter la méthode ultra si disponible
        if self.ultra_predictor:
            self.prediction_methods['ultra_optimized'] = self._ultra_optimized_prediction

        # Ajouter les méthodes ML si disponibles
        if self.ml_model:
            self.prediction_methods.update({
                'ml_probability': self._ml_probability_prediction,
                'ml_threshold': self._ml_threshold_prediction,
                'ml_hybrid': self._ml_hybrid_prediction
            })

        # Initialiser les modèles améliorés
        self.enhanced_ml = None
        self.adaptive_predictor = None
        self.advanced_backtest = None

        try:
            from enhanced_ml_model import EnhancedKenoMLModel
            self.enhanced_ml = EnhancedKenoMLModel()
            self.prediction_methods['enhanced_ml'] = self._enhanced_ml_prediction
            logger.info("Modèle ML amélioré initialisé")
        except ImportError:
            logger.info("Modèle ML amélioré non disponible")

        try:
            from adaptive_predictor import AdaptiveKenoPredictor
            self.adaptive_predictor = AdaptiveKenoPredictor()
            self.prediction_methods['adaptive'] = self._adaptive_prediction
            logger.info("Prédicteur adaptatif initialisé")
        except ImportError:
            logger.info("Prédicteur adaptatif non disponible")

        try:
            from advanced_backtest_engine import AdvancedBacktestEngine
            self.advanced_backtest = AdvancedBacktestEngine()
            logger.info("Moteur de backtest avancé initialisé")
        except ImportError:
            logger.info("Moteur de backtest avancé non disponible")
        
        # Paramètres pour le double roulement
        self.double_roll_enabled = False
        self.roll_history = []  # Historique des roulements
        
        # Système Full Auto
        self.performance_cache = {}  # Cache des performances des méthodes
        self.last_evaluation = None  # Dernière évaluation complète
        self.best_method = None  # Meilleure méthode calculée
    
    def initialize_weights(self):
        """Initialise les poids pour la prédiction."""
        if self.analyzer.data is not None:
            self.weights = self.analyzer.get_prediction_weights()
        else:
            # Poids uniformes si pas de données
            self.weights = {i: 1.0 for i in range(1, 71)}
    
    def _weighted_random_prediction(self, num_predictions):
        """
        Prédiction basée sur les poids statistiques.
        
        Args:
            num_predictions (int): Nombre de numéros à prédire
            
        Returns:
            list: Liste des numéros prédits
        """
        if not self.weights:
            self.initialize_weights()
        
        numbers = list(range(1, 71))
        weights = [self.weights[num] for num in numbers]
        
        # Normaliser les poids
        total_weight = sum(weights)
        probabilities = [w / total_weight for w in weights]
        
        # Sélection pondérée sans remise
        selected = []
        available_numbers = numbers.copy()
        available_probs = probabilities.copy()
        
        for _ in range(num_predictions):
            if not available_numbers:
                break
                
            # Normaliser les probabilités restantes
            total_prob = sum(available_probs)
            if total_prob > 0:
                normalized_probs = [p / total_prob for p in available_probs]
                
                # Sélectionner un numéro
                np = _lazy_np()
                selected_idx = np.random.choice(len(available_numbers), p=normalized_probs)
                selected_num = available_numbers[selected_idx]
                
                selected.append(selected_num)
                
                # Retirer le numéro sélectionné
                available_numbers.pop(selected_idx)
                available_probs.pop(selected_idx)
        
        return sorted(selected)
    
    def _hot_numbers_prediction(self, num_predictions):
        """
        Prédiction basée sur les numéros chauds récents.
        
        Args:
            num_predictions (int): Nombre de numéros à prédire
            
        Returns:
            list: Liste des numéros prédits
        """
        hot_cold = self.analyzer.find_hot_cold_numbers()
        hot_numbers = hot_cold.get('hot', [])
        
        # Compléter avec des numéros aléatoires si pas assez de numéros chauds
        if len(hot_numbers) < num_predictions:
            all_numbers = set(range(1, 71))
            remaining_numbers = list(all_numbers - set(hot_numbers))
            additional_needed = num_predictions - len(hot_numbers)
            additional_numbers = random.sample(remaining_numbers, 
                                             min(additional_needed, len(remaining_numbers)))
            hot_numbers.extend(additional_numbers)
        
        return sorted(hot_numbers[:num_predictions])
    
    def _balanced_prediction(self, num_predictions):
        """
        Prédiction équilibrée entre numéros chauds, moyens et froids.
        
        Args:
            num_predictions (int): Nombre de numéros à prédire
            
        Returns:
            list: Liste des numéros prédits
        """
        hot_cold = self.analyzer.find_hot_cold_numbers()
        all_hot = hot_cold.get('hot', [])
        all_cold = hot_cold.get('cold', [])
        
        # Fix: Sélection aléatoire au lieu de toujours prendre les premiers
        # Cela évite le pattern déterministe 3, 27, 48, 51
        hot_needed = max(1, num_predictions // 4)  # Environ 25% de numéros chauds
        cold_needed = max(1, num_predictions // 5)  # Environ 20% de numéros froids
        
        # Sélection aléatoire parmi les numéros chauds et froids
        hot_numbers = []
        if all_hot and hot_needed > 0:
            hot_numbers = random.sample(all_hot, min(hot_needed, len(all_hot)))
        
        cold_numbers = []
        if all_cold and cold_needed > 0:
            cold_numbers = random.sample(all_cold, min(cold_needed, len(all_cold)))
        
        # Numéros moyens (ni chauds ni froids)
        all_numbers = set(range(1, 71))
        hot_cold_set = set(all_hot + all_cold)  # Exclure TOUS les hot/cold, pas seulement les sélectionnés
        medium_numbers = list(all_numbers - hot_cold_set)
        
        remaining_needed = num_predictions - len(hot_numbers) - len(cold_numbers)
        medium_selected = []
        if remaining_needed > 0 and medium_numbers:
            medium_selected = random.sample(medium_numbers, 
                                          min(remaining_needed, len(medium_numbers)))
        
        prediction = hot_numbers + cold_numbers + medium_selected
        
        # Compléter si nécessaire avec n'importe quels numéros restants
        if len(prediction) < num_predictions:
            remaining = list(all_numbers - set(prediction))
            additional = random.sample(remaining, num_predictions - len(prediction))
            prediction.extend(additional)
        
        return sorted(prediction[:num_predictions])
    
    def _pattern_based_prediction(self, num_predictions):
        """
        Prédiction basée sur les patterns identifiés.
        
        Args:
            num_predictions (int): Nombre de numéros à prédire
            
        Returns:
            list: Liste des numéros prédits
        """
        if not self.analyzer.patterns:
            self.analyzer.analyze_number_patterns()
        
        prediction = []
        
        # Utiliser les paires consécutives fréquentes
        consecutive_pairs = self.analyzer.patterns.get('consecutive_pairs', {})
        if consecutive_pairs:
            # Prendre les paires les plus fréquentes
            top_pairs = sorted(consecutive_pairs.items(), key=lambda x: x[1], reverse=True)
            for (num1, num2), freq in top_pairs[:num_predictions//4]:
                if len(prediction) < num_predictions:
                    if num1 not in prediction:
                        prediction.append(num1)
                    if num2 not in prediction and len(prediction) < num_predictions:
                        prediction.append(num2)
        
        # Compléter avec une sélection pondérée
        if len(prediction) < num_predictions:
            remaining_needed = num_predictions - len(prediction)
            remaining_numbers = [n for n in range(1, 71) if n not in prediction]
            
            if remaining_numbers:
                if self.weights:
                    # Sélection pondérée des numéros restants
                    weights = [self.weights[num] for num in remaining_numbers]
                    total_weight = sum(weights)
                    if total_weight > 0:
                        probs = [w / total_weight for w in weights]
                        np = _lazy_np()
                        additional = np.random.choice(remaining_numbers,
                                                    size=min(remaining_needed, len(remaining_numbers)),
                                                    replace=False, p=probs).tolist()
                    else:
                        additional = random.sample(remaining_numbers, 
                                                 min(remaining_needed, len(remaining_numbers)))
                else:
                    additional = random.sample(remaining_numbers, 
                                             min(remaining_needed, len(remaining_numbers)))
                
                prediction.extend(additional)
        
        return sorted(prediction[:num_predictions])
    
    def _anti_pattern_prediction(self, num_predictions):
        """
        Prédiction anti-pattern (éviter les patterns fréquents).
        
        Args:
            num_predictions (int): Nombre de numéros à prédire
            
        Returns:
            list: Liste des numéros prédits
        """
        # Éviter les numéros trop chauds et privilégier les numéros moyens/froids
        hot_cold = self.analyzer.find_hot_cold_numbers()
        hot_numbers = set(hot_cold.get('hot', []))
        
        # Privilégier les numéros non-chauds
        available_numbers = [n for n in range(1, 71) if n not in hot_numbers]
        
        if len(available_numbers) >= num_predictions:
            prediction = random.sample(available_numbers, num_predictions)
        else:
            # Si pas assez de numéros non-chauds, compléter avec tous les numéros
            prediction = available_numbers.copy()
            remaining = [n for n in range(1, 71) if n not in prediction]
            additional_needed = num_predictions - len(prediction)
            prediction.extend(random.sample(remaining, additional_needed))
        
        return sorted(prediction)

    def _ml_probability_prediction(self, num_predictions):
        """
        Prédiction ML basée sur les probabilités CatBoost.

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
        """
        if not self.ml_model or not self.analyzer.data is not None:
            logger.warning("Modèle ML non disponible, utilisation de la méthode weighted_random")
            return self._weighted_random_prediction(num_predictions)

        try:
            # Utiliser le dernier tirage comme référence pour les features
            target_index = len(self.analyzer.data) - 1

            # Charger le modèle si nécessaire
            if not self.ml_model.is_trained:
                if not self.ml_model.load_models():
                    logger.warning("Aucun modèle ML entraîné, utilisation de weighted_random")
                    return self._weighted_random_prediction(num_predictions)

            # Prédiction ML
            prediction = self.ml_model.predict_numbers(
                self.analyzer.data, target_index, num_predictions, method='probability'
            )

            return prediction

        except Exception as e:
            logger.error(f"Erreur lors de la prédiction ML: {e}")
            return self._weighted_random_prediction(num_predictions)

    def _ml_threshold_prediction(self, num_predictions):
        """
        Prédiction ML basée sur un seuil de probabilité.

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
        """
        if not self.ml_model or not self.analyzer.data is not None:
            return self._weighted_random_prediction(num_predictions)

        try:
            target_index = len(self.analyzer.data) - 1

            if not self.ml_model.is_trained:
                if not self.ml_model.load_models():
                    return self._weighted_random_prediction(num_predictions)

            prediction = self.ml_model.predict_numbers(
                self.analyzer.data, target_index, num_predictions, method='threshold'
            )

            return prediction

        except Exception as e:
            logger.error(f"Erreur lors de la prédiction ML threshold: {e}")
            return self._weighted_random_prediction(num_predictions)

    def _ml_hybrid_prediction(self, num_predictions):
        """
        Prédiction hybride combinant ML et méthodes statistiques.

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
        """
        if not self.ml_model or not self.analyzer.data is not None:
            return self._balanced_prediction(num_predictions)

        try:
            # Obtenir les prédictions ML
            ml_prediction = self._ml_probability_prediction(num_predictions // 2)

            # Obtenir les prédictions statistiques
            stat_prediction = self._pattern_based_prediction(num_predictions - len(ml_prediction))

            # Combiner en évitant les doublons
            combined = list(ml_prediction)
            for num in stat_prediction:
                if num not in combined and len(combined) < num_predictions:
                    combined.append(num)

            # Compléter si nécessaire
            if len(combined) < num_predictions:
                remaining = [n for n in range(1, 71) if n not in combined]
                additional = random.sample(remaining, num_predictions - len(combined))
                combined.extend(additional)

            return sorted(combined[:num_predictions])

        except Exception as e:
            logger.error(f"Erreur lors de la prédiction hybride: {e}")
            return self._balanced_prediction(num_predictions)

    def _enhanced_ml_prediction(self, num_predictions):
        """
        Prédiction avec le modèle ML amélioré (ensemble sophistiqué).

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
        """
        if not self.enhanced_ml:
            logger.warning("Modèle ML amélioré non disponible, utilisation de ml_hybrid")
            return self._ml_hybrid_prediction(num_predictions) if self.ml_model else self._balanced_prediction(num_predictions)

        try:
            if not self.enhanced_ml.is_trained:
                logger.info("Entraînement du modèle ML amélioré...")
                if self.analyzer.data is not None and len(self.analyzer.data) > 200:
                    self.enhanced_ml.train_models(self.analyzer.data, max_samples=5000)
                else:
                    logger.warning("Données insuffisantes pour l'entraînement amélioré")
                    return self._balanced_prediction(num_predictions)

            # Prédiction avec le modèle amélioré
            target_index = len(self.analyzer.data) - 1
            predictions = self.enhanced_ml.predict_numbers(
                self.analyzer.data, target_index, num_predictions, method='probability'
            )

            logger.info(f"Prédiction ML améliorée: {predictions}")
            return predictions

        except Exception as e:
            logger.error(f"Erreur prédiction ML améliorée: {e}")
            return self._ml_hybrid_prediction(num_predictions) if self.ml_model else self._balanced_prediction(num_predictions)

    def _adaptive_prediction(self, num_predictions):
        """
        Prédiction adaptative qui s'optimise automatiquement.

        Args:
            num_predictions (int): Nombre de numéros à prédire

        Returns:
            list: Liste des numéros prédits
        """
        if not self.adaptive_predictor:
            logger.warning("Prédicteur adaptatif non disponible, utilisation de enhanced_ml")
            return self._enhanced_ml_prediction(num_predictions)

        try:
            # Utiliser la prédiction adaptative
            result = self.adaptive_predictor.predict_adaptive(num_predictions)
            predictions = result.get('predictions', [])

            # Log des informations adaptatives
            strategy = result.get('strategy', {}).get('name', 'unknown')
            confidence = result.get('confidence', 0)
            patterns_detected = result.get('patterns_detected', 0)

            logger.info(f"Prédiction adaptative: {predictions}")
            logger.info(f"Stratégie: {strategy}, Confiance: {confidence:.1%}, Patterns: {patterns_detected}")

            return predictions

        except Exception as e:
            logger.error(f"Erreur prédiction adaptative: {e}")
            return self._enhanced_ml_prediction(num_predictions)

    def _ultra_optimized_prediction(self, num_predictions):
        """
        Prédiction ultra-optimisée avec toutes les améliorations.
        
        Args:
            num_predictions (int): Nombre de numéros à prédire
            
        Returns:
            list: Liste des numéros prédits
        """
        if not self.ultra_predictor:
            logger.warning("Prédicteur ultra non disponible, utilisation de la méthode hybride")
            return self._ml_hybrid_prediction(num_predictions) if self.ml_model else self._balanced_prediction(num_predictions)
        
        try:
            # Préférer un chemin non bloquant: si Ultra n'est pas prêt, fallback rapide
            is_ready = False
            try:
                if hasattr(self.ultra_predictor, "is_ready"):
                    is_ready = bool(self.ultra_predictor.is_ready())
                else:
                    is_ready = bool(getattr(self.ultra_predictor, "is_trained", False))
            except Exception:
                is_ready = bool(getattr(self.ultra_predictor, "is_trained", False))
            
            if not is_ready:
                logger.info("Ultra non prêt: fallback 'balanced' et amorçage en arrière-plan")
                # Amorcer l'entraînement/caches en arrière-plan sans bloquer
                try:
                    import threading
                    def _warm():
                        try:
                            if hasattr(self.ultra_predictor, "train_ultra_model"):
                                self.ultra_predictor.train_ultra_model()
                            elif hasattr(self.ultra_predictor, "train"):
                                self.ultra_predictor.train()
                            # Pré-chauffer les caches si possible
                            if hasattr(self.ultra_predictor, "analyzer") and hasattr(self.ultra_predictor, "cache"):
                                data = getattr(self.ultra_predictor.analyzer, "data", None)
                                if data is not None:
                                    try:
                                        self.ultra_predictor.analyze_ultra_patterns(data)
                                    except Exception:
                                        pass
                                    try:
                                        self.ultra_predictor.calculate_ultra_frequencies(data)
                                    except Exception:
                                        pass
                        except Exception:
                            pass
                    threading.Thread(target=_warm, daemon=True).start()
                except Exception:
                    pass
                return self._balanced_prediction(num_predictions)
            
            # Ultra prêt: effectuer la prédiction
            if hasattr(self.ultra_predictor, "predict_ultra_numbers"):
                prediction = self.ultra_predictor.predict_ultra_numbers(num_predictions)
            else:
                prediction = self.ultra_predictor.predict(num_predictions)
            return prediction
            
        except Exception as e:
            logger.error(f"Erreur lors de la prédiction ultra: {e}")
            return self._balanced_prediction(num_predictions)

    def train_ml_model(self, max_samples=5000):
        """
        Entraîne le modèle ML avec les données disponibles.

        Args:
            max_samples (int): Nombre maximum d'échantillons pour l'entraînement

        Returns:
            dict: Métriques d'entraînement ou None si échec
        """
        if not self.ml_model or self.analyzer.data is None:
            logger.error("Modèle ML ou données non disponibles")
            return None

        try:
            logger.info("Début de l'entraînement du modèle ML...")
            metrics = self.ml_model.train_models(
                self.analyzer.data,
                max_samples=max_samples,
                min_history=50
            )

            logger.info("Entraînement ML terminé avec succès")
            return metrics

        except Exception as e:
            logger.error(f"Erreur lors de l'entraînement ML: {e}")
            return None

    def train_ultra_model(self, max_samples: int = 5000) -> dict:
        """
        Entraînement 'ultra' centralisé pour satisfaire l'UI.
        Stratégie:
          1) Si ultra_predictor est disponible, appeler son entraînement.
          2) Sinon, entraîner le modèle ML si disponible.
          3) Mettre à jour/mettre en cache les poids depuis l'analyseur.
        Returns:
          dict: métriques ou informations de statut.
        """
        metrics = {"status": "noop"}
        try:
            # 1) Entraînement du prédicteur ultra si présent
            if self.ultra_predictor is not None:
                try:
                    if hasattr(self.ultra_predictor, "train_ultra_model"):
                        self.ultra_predictor.train_ultra_model()
                    elif hasattr(self.ultra_predictor, "train"):
                        self.ultra_predictor.train()
                    metrics["ultra_trained"] = True
                    metrics["status"] = "ultra_trained"
                except Exception as e:
                    logger.warning(f"Échec entraînement ultra_predictor: {e}")
                    metrics["ultra_trained"] = False
            
            # 2) Entraînement ML en fallback si dispo
            if self.ml_model is not None:
                try:
                    ml_metrics = self.train_ml_model(max_samples=max_samples)
                    metrics["ml_trained"] = ml_metrics is not None
                    if ml_metrics:
                        metrics["ml_metrics"] = ml_metrics
                        metrics["status"] = "ml_trained"
                except Exception as e:
                    logger.warning(f"Échec entraînement ML: {e}")
                    metrics["ml_trained"] = False
            
            # 3) Rafraîchir/mettre en cache les poids
            try:
                self.initialize_weights()
                metrics["weights_initialized"] = True
            except Exception as e:
                logger.warning(f"Échec initialisation des poids: {e}")
                metrics["weights_initialized"] = False
            
            # Si rien de concret n'a été entraîné
            if metrics.get("status") == "noop":
                metrics["status"] = "no_trainer_available"
            return metrics
        except Exception as e:
            logger.error(f"Erreur train_ultra_model: {e}")
            return {"status": "error", "error": str(e)}
    
    def get_ml_probabilities(self):
        """
        Retourne les probabilités ML pour tous les numéros.

        Returns:
            dict: Probabilités pour chaque numéro ou None si ML non disponible
        """
        if not self.ml_model or self.analyzer.data is None:
            return None

        try:
            target_index = len(self.analyzer.data) - 1

            if not self.ml_model.is_trained:
                if not self.ml_model.load_models():
                    return None

            probabilities = self.ml_model.get_prediction_probabilities(
                self.analyzer.data, target_index
            )

            return probabilities

        except Exception as e:
            logger.error(f"Erreur lors de l'obtention des probabilités ML: {e}")
            return None

    def predict(self, num_predictions, method='weighted_random', num_sets=1):
        """
        Génère des prédictions de numéros Keno.
        
        Args:
            num_predictions (int): Nombre de numéros à prédire (2-10)
            method (str): Méthode de prédiction à utiliser
            num_sets (int): Nombre de jeux à générer
            
        Returns:
            list: Liste des prédictions (chaque prédiction est une liste de numéros)
        """
        if not (2 <= num_predictions <= 10):
            raise ValueError("Le nombre de prédictions doit être entre 2 et 10")
        
        if method not in self.prediction_methods:
            raise ValueError(f"Méthode inconnue: {method}. Méthodes disponibles: {list(self.prediction_methods.keys())}")
        
        predictions = []
        
        for set_num in range(num_sets):
            if self.double_roll_enabled:
                # Utiliser le système de double roulement
                logger.info(f"🎲 Génération avec double roulement (grille {set_num + 1}/{num_sets})")
                prediction = self._double_roll_enhanced_prediction(method, num_predictions)
            else:
                # Méthode normale
                prediction_func = self.prediction_methods[method]
                prediction = prediction_func(num_predictions)
            
            predictions.append(prediction)
        
        return predictions
    
    def predict_with_double_roll(self, num_predictions, method='weighted_random', num_sets=1):
        """
        Génère des prédictions avec le système de double roulement forcé.
        
        Args:
            num_predictions (int): Nombre de numéros à prédire (2-10)
            method (str): Méthode de prédiction à utiliser
            num_sets (int): Nombre de jeux à générer
            
        Returns:
            tuple: (predictions, roll_details) - Prédictions et détails des roulements
        """
        # Activer temporairement le double roulement
        original_state = self.double_roll_enabled
        self.enable_double_roll(True)
        
        try:
            # Réinitialiser l'historique pour cette session
            self.roll_history = []
            
            # Générer les prédictions
            predictions = self.predict(num_predictions, method, num_sets)
            
            # Collecter les détails des roulements
            roll_details = {
                'total_rolls': len(self.roll_history),
                'roll_history': self.roll_history.copy(),
                'method_used': method,
                'sets_generated': num_sets
            }
            
            return predictions, roll_details
            
        finally:
            # Restaurer l'état original
            self.double_roll_enabled = original_state
    
    def get_prediction_confidence(self, prediction):
        """
        Calcule un score de confiance pour une prédiction.
        
        Args:
            prediction (list): Liste des numéros prédits
            
        Returns:
            float: Score de confiance (0-100)
        """
        if not self.weights:
            return 50.0  # Confiance neutre sans données
        
        # Calculer la confiance basée sur les poids
        total_weight = sum(self.weights[num] for num in prediction)
        avg_weight = total_weight / len(prediction)
        
        # Normaliser le score (poids moyen de 1.0 = 50% de confiance)
        confidence = min(100, max(0, avg_weight * 50))
        
        return confidence
    
    def get_method_description(self, method):
        """
        Retourne la description d'une méthode de prédiction.

        Args:
            method (str): Nom de la méthode

        Returns:
            str: Description de la méthode
        """
        descriptions = {
            'weighted_random': "Sélection aléatoire pondérée basée sur les fréquences historiques",
            'hot_numbers': "Privilégie les numéros sortis fréquemment récemment",
            'balanced': "Équilibre entre numéros chauds, moyens et froids",
            'pattern_based': "Basé sur les patterns et paires fréquentes identifiés",
            'anti_pattern': "Évite les patterns trop fréquents (stratégie contraire)",
            'ml_probability': "🤖 Machine Learning - Sélection par probabilités CatBoost",
            'ml_threshold': "🤖 Machine Learning - Sélection par seuil de probabilité",
            'ml_hybrid': "🤖 Hybride ML + Statistiques - Combine CatBoost et patterns",
            'ultra_optimized': "🚀 ULTRA-OPTIMISÉ - IA avancée + Ensemble ML + Patterns Ultra"
        }
        return descriptions.get(method, "Méthode inconnue")
    
    def enable_double_roll(self, enabled=True):
        """
        Active ou désactive le système de double roulement.
        
        Args:
            enabled (bool): True pour activer, False pour désactiver
        """
        self.double_roll_enabled = enabled
        if enabled:
            logger.info("🎲 Double roulement activé - Chance + Chance")
        else:
            logger.info("🎲 Double roulement désactivé")
    
    def perform_chance_roll(self, method='balanced'):
        """
        Effectue un roulement "chance" en générant des nombres temporaires.
        
        Args:
            method (str): Méthode à utiliser pour le roulement
            
        Returns:
            list: Résultat du roulement chance
        """
        np = _lazy_np()
        
        # Générer entre 3 et 7 numéros pour le roulement chance
        num_chance = random.randint(3, 7)
        
        if method in self.prediction_methods:
            chance_numbers = self.prediction_methods[method](num_chance)
        else:
            # Fallback vers balanced si méthode inconnue
            chance_numbers = self._balanced_prediction(num_chance)
        
        # Ajouter à l'historique des roulements
        self.roll_history.append({
            'type': 'chance',
            'numbers': chance_numbers,
            'method': method,
            'timestamp': random.randint(1000, 9999)  # Simulation timestamp
        })
        
        logger.info(f"🎲 Roulement chance: {chance_numbers} (méthode: {method})")
        return chance_numbers
    
    def calculate_double_roll_influence(self):
        """
        Calcule l'influence des deux roulements chance sur la prédiction finale.
        
        Returns:
            dict: Influence de chaque numéro basée sur les roulements
        """
        if len(self.roll_history) < 2:
            return {}
        
        # Prendre les deux derniers roulements
        roll1 = self.roll_history[-2]['numbers']
        roll2 = self.roll_history[-1]['numbers'] 
        
        influence = {}
        
        # Numéros présents dans les deux roulements = influence très forte
        for num in set(roll1) & set(roll2):
            influence[num] = 3.0  # Triple influence
        
        # Numéros présents dans un seul roulement = influence forte
        for num in set(roll1) | set(roll2):
            if num not in influence:
                influence[num] = 2.0  # Double influence
        
        # Numéros adjacents aux roulements = influence modérée
        all_roll_numbers = set(roll1) | set(roll2)
        for num in all_roll_numbers:
            # Numéros adjacents (±1, ±2)
            for offset in [-2, -1, 1, 2]:
                adjacent = num + offset
                if 1 <= adjacent <= 70 and adjacent not in influence:
                    influence[adjacent] = 1.5  # Influence modérée
        
        logger.info(f"🎲 Influence double roulement calculée: {len(influence)} numéros affectés")
        return influence
    
    def _double_roll_enhanced_prediction(self, base_method, num_predictions):
        """
        Génère une prédiction améliorée par le double roulement.
        
        Args:
            base_method (str): Méthode de base à utiliser
            num_predictions (int): Nombre de prédictions à générer
            
        Returns:
            list: Prédiction finale influencée par les roulements
        """
        np = _lazy_np()
        
        # Effectuer les deux roulements chance
        self.perform_chance_roll(base_method)
        self.perform_chance_roll(base_method)
        
        # Calculer l'influence des roulements
        roll_influence = self.calculate_double_roll_influence()
        
        # Générer une prédiction de base
        if base_method in self.prediction_methods:
            base_prediction = self.prediction_methods[base_method](num_predictions * 2)  # Plus de candidats
        else:
            base_prediction = self._balanced_prediction(num_predictions * 2)
        
        # Appliquer l'influence des roulements
        influenced_weights = {}
        for num in range(1, 71):
            base_weight = self.weights.get(num, 1.0)
            roll_multiplier = roll_influence.get(num, 1.0)
            influenced_weights[num] = base_weight * roll_multiplier
        
        # Sélectionner les meilleurs numéros influencés
        # Mélanger prédiction de base et influence des roulements
        candidates = []
        
        # 70% de la prédiction vient de l'influence des roulements
        roll_candidates = sorted(roll_influence.keys(), 
                               key=lambda x: influenced_weights.get(x, 0), 
                               reverse=True)[:int(num_predictions * 0.7) + 2]
        candidates.extend(roll_candidates)
        
        # 30% vient de la prédiction de base
        base_candidates = [num for num in base_prediction if num not in candidates][:int(num_predictions * 0.3) + 2]
        candidates.extend(base_candidates)
        
        # Compléter si pas assez de candidats
        while len(candidates) < num_predictions:
            remaining = [i for i in range(1, 71) if i not in candidates]
            if remaining:
                weights_remaining = [influenced_weights.get(num, 1.0) for num in remaining]
                selected = np.random.choice(remaining, p=np.array(weights_remaining)/sum(weights_remaining))
                candidates.append(int(selected))
            else:
                break
        
        # Sélectionner la prédiction finale
        final_prediction = sorted(candidates[:num_predictions])
        
        logger.info(f"🎲 Prédiction finale avec double roulement: {final_prediction}")
        return final_prediction

    def evaluate_predictor_performance(self, method, sample_size=50, test_predictions=7):
        """
        Évalue les performances d'un prédicteur en testant sur données historiques.
        
        Args:
            method (str): Méthode à évaluer
            sample_size (int): Nombre de tests à effectuer
            test_predictions (int): Nombre de prédictions par test
            
        Returns:
            dict: Métriques de performance
        """
        if method not in self.prediction_methods:
            return None
        
        if self.analyzer.data is None or len(self.analyzer.data) < sample_size + 10:
            return {'score': 0.0, 'accuracy': 0.0, 'diversity': 0.0, 'error': 'Pas assez de données'}
        
        try:
            np = _lazy_np()
            total_hits = 0
            total_predictions = 0
            unique_numbers = set()
            
            # Tester sur les derniers tirages
            for i in range(sample_size):
                try:
                    # Prendre un tirage aléatoire dans les données récentes
                    test_idx = len(self.analyzer.data) - 1 - i
                    if test_idx < 0:
                        continue
                    
                    actual_draw = self.analyzer.data.iloc[test_idx]
                    actual_numbers = set()
                    
                    # Extraire les numéros gagnants du tirage
                    for j in range(1, 21):  # boule1 à boule20
                        col_name = f'boule{j}'
                        if col_name in actual_draw and not pd.isna(actual_draw[col_name]):
                            actual_numbers.add(int(actual_draw[col_name]))
                    
                    if len(actual_numbers) < 10:  # Vérifier que le tirage est valide
                        continue
                    
                    # Générer une prédiction avec cette méthode
                    prediction = self.prediction_methods[method](test_predictions)
                    predicted_set = set(prediction)
                    
                    # Calculer les hits
                    hits = len(predicted_set & actual_numbers)
                    total_hits += hits
                    total_predictions += len(prediction)
                    unique_numbers.update(prediction)
                    
                except Exception:
                    continue
            
            if total_predictions == 0:
                return {'score': 0.0, 'accuracy': 0.0, 'diversity': 0.0, 'error': 'Aucun test valide'}
            
            # Calculer les métriques
            accuracy = (total_hits / total_predictions) * 100  # % de numéros corrects
            diversity = (len(unique_numbers) / 70) * 100  # % de couverture des numéros
            
            # Score composite (pondération: 70% précision, 30% diversité)
            score = (accuracy * 0.7) + (diversity * 0.3)
            
            return {
                'score': round(score, 2),
                'accuracy': round(accuracy, 2),
                'diversity': round(diversity, 2),
                'total_hits': total_hits,
                'total_predictions': total_predictions,
                'unique_numbers': len(unique_numbers),
                'tests_performed': sample_size
            }
            
        except Exception as e:
            logger.error(f"Erreur évaluation {method}: {e}")
            return {'score': 0.0, 'accuracy': 0.0, 'diversity': 0.0, 'error': str(e)}

    def find_best_predictor(self, force_reevaluation=False):
        """
        Trouve automatiquement le meilleur prédicteur basé sur les performances.
        
        Args:
            force_reevaluation (bool): Forcer la réévaluation même si cache disponible
            
        Returns:
            str: Nom de la meilleure méthode
        """
        # Vérifier le cache (valide 1 heure)
        import time
        cache_duration = 3600  # 1 heure
        
        if (not force_reevaluation and 
            self.last_evaluation and 
            time.time() - self.last_evaluation < cache_duration and
            self.best_method):
            logger.info(f"🔍 Meilleur prédicteur (cache): {self.best_method}")
            return self.best_method
        
        logger.info("🔍 Évaluation des prédicteurs en cours...")
        
        # Évaluer toutes les méthodes disponibles
        method_scores = {}
        
        for method_name in self.prediction_methods.keys():
            # Skip les méthodes ML si pas disponibles pour éviter les erreurs
            if method_name.startswith('ml_') and not self.is_ml_available():
                continue
                
            logger.info(f"  📊 Évaluation: {method_name}...")
            performance = self.evaluate_predictor_performance(method_name)
            
            if performance and 'error' not in performance:
                method_scores[method_name] = performance
                logger.info(f"    Score: {performance['score']:.1f}% | Précision: {performance['accuracy']:.1f}% | Diversité: {performance['diversity']:.1f}%")
            else:
                logger.warning(f"    Échec évaluation {method_name}: {performance.get('error', 'Erreur inconnue')}")
        
        if not method_scores:
            # Fallback vers balanced si aucune évaluation réussie
            logger.warning("🔍 Aucune évaluation réussie, utilisation de 'balanced'")
            self.best_method = 'balanced'
            return 'balanced'
        
        # Trouver la meilleure méthode
        best_method = max(method_scores.keys(), key=lambda x: method_scores[x]['score'])
        best_score = method_scores[best_method]['score']
        
        # Mettre à jour le cache
        self.performance_cache = method_scores
        self.best_method = best_method
        self.last_evaluation = time.time()
        
        logger.info(f"🏆 Meilleur prédicteur: {best_method} (score: {best_score:.1f}%)")
        
        return best_method

    def predict_full_auto(self, num_predictions, num_sets=1):
        """
        Prédiction Full Auto avec le meilleur prédicteur + double roulement.
        
        Args:
            num_predictions (int): Nombre de numéros à prédire (2-10)
            num_sets (int): Nombre de jeux à générer
            
        Returns:
            tuple: (predictions, details) - Prédictions et détails complets
        """
        logger.info("🚀 PRÉDICTION FULL AUTO - Recherche du meilleur prédicteur...")
        
        # Trouver automatiquement le meilleur prédicteur
        best_method = self.find_best_predictor()
        
        logger.info(f"🎯 Méthode sélectionnée: {best_method}")
        logger.info(f"📝 Description: {self.get_method_description(best_method)}")
        
        # Utiliser le double roulement avec la meilleure méthode
        predictions, roll_details = self.predict_with_double_roll(
            num_predictions, best_method, num_sets
        )
        
        # Ajouter les informations Full Auto
        full_auto_details = {
            **roll_details,
            'full_auto': True,
            'selected_method': best_method,
            'method_description': self.get_method_description(best_method),
            'performance_data': self.performance_cache.get(best_method, {}),
            'evaluation_time': self.last_evaluation
        }
        
        return predictions, full_auto_details

    def is_ml_available(self):
        """
        Vérifie si le ML est disponible et fonctionnel.

        Returns:
            bool: True si le ML est disponible
        """
        return self.ml_model is not None and ML_AVAILABLE

    def get_ml_status(self):
        """
        Retourne le statut du modèle ML.

        Returns:
            dict: Informations sur le statut du ML
        """
        if not self.is_ml_available():
            return {
                'available': False,
                'trained': False,
                'models_count': 0,
                'status': 'ML non disponible'
            }

        try:
            # Vérifier si des modèles sont entraînés
            trained = self.ml_model.is_trained or self.ml_model.load_models()
            models_count = len(self.ml_model.models) if trained else 0

            status = 'Prêt' if trained else 'Non entraîné'

            return {
                'available': True,
                'trained': trained,
                'models_count': models_count,
                'status': status
            }

        except Exception as e:
            return {
                'available': True,
                'trained': False,
                'models_count': 0,
                'status': f'Erreur: {e}'
            }

    def run_advanced_backtest(self, methods: List[str] = None,
                            test_size: int = 100) -> Dict:
        """
        Exécute un backtest avancé avec métriques sophistiquées.

        Args:
            methods (List[str]): Méthodes à tester
            test_size (int): Taille de l'échantillon de test

        Returns:
            dict: Résultats détaillés du backtest
        """
        if not self.analyzer.data or len(self.analyzer.data) < test_size + 100:
            return {'error': 'Données insuffisantes pour le backtest avancé'}

        if methods is None:
            methods = ['enhanced_ml', 'adaptive', 'ml_hybrid', 'balanced', 'pattern_based']

        logger.info(f"🚀 Backtest avancé: {len(methods)} méthodes sur {test_size} tirages")

        try:
            if self.advanced_backtest:
                results = self.advanced_backtest.run_comprehensive_backtest(
                    self, self.analyzer.data, methods
                )

                # Extraire les informations clés
                summary = {
                    'timestamp': results.get('timestamp'),
                    'methods_tested': methods,
                    'best_method': results.get('comparative_analysis', {}).get('best_method'),
                    'ranking': results.get('comparative_analysis', {}).get('ranking', []),
                    'recommendations': results.get('recommendations', []),
                    'detailed_results': results
                }

                logger.info(f"✅ Backtest terminé. Meilleure méthode: {summary['best_method']}")
                return summary
            else:
                # Fallback vers le backtest simple
                return self.evaluate_all_methods(sample_size=min(test_size, 50))

        except Exception as e:
            logger.error(f"Erreur backtest avancé: {e}")
            return {'error': str(e)}

    def auto_optimize_system(self, optimization_cycles: int = 3) -> Dict:
        """
        Système d'auto-optimisation qui améliore automatiquement les performances.

        Args:
            optimization_cycles (int): Nombre de cycles d'optimisation

        Returns:
            dict: Résultats de l'optimisation
        """
        logger.info(f"🔄 Démarrage auto-optimisation ({optimization_cycles} cycles)")

        optimization_results = {
            'timestamp': datetime.now().isoformat(),
            'cycles_completed': 0,
            'improvements': [],
            'final_performance': {},
            'best_configuration': {},
            'optimization_history': []
        }

        try:
            # Évaluation initiale
            initial_performance = self.run_advanced_backtest()
            optimization_results['initial_performance'] = initial_performance

            current_best_method = initial_performance.get('best_method', 'balanced')
            current_best_score = 0

            if initial_performance.get('ranking'):
                current_best_score = initial_performance['ranking'][0][1]

            logger.info(f"Performance initiale: {current_best_method} (score: {current_best_score:.3f})")

            for cycle in range(optimization_cycles):
                logger.info(f"🔧 Cycle d'optimisation {cycle + 1}/{optimization_cycles}")

                cycle_improvements = []

                # 1. Optimiser les modèles ML
                if self.enhanced_ml:
                    try:
                        logger.info("Réentraînement du modèle ML amélioré...")
                        ml_metrics = self.enhanced_ml.train_models(
                            self.analyzer.data, max_samples=8000
                        )
                        if ml_metrics:
                            cycle_improvements.append({
                                'type': 'ml_retraining',
                                'metrics': ml_metrics
                            })
                    except Exception as e:
                        logger.warning(f"Erreur réentraînement ML: {e}")

                # 2. Optimiser le prédicteur adaptatif
                if self.adaptive_predictor:
                    try:
                        # Forcer une adaptation
                        self.adaptive_predictor._perform_adaptation()
                        cycle_improvements.append({
                            'type': 'adaptive_optimization',
                            'status': 'completed'
                        })
                    except Exception as e:
                        logger.warning(f"Erreur optimisation adaptative: {e}")

                # 3. Réévaluer les performances
                cycle_performance = self.run_advanced_backtest()

                new_best_method = cycle_performance.get('best_method', current_best_method)
                new_best_score = 0

                if cycle_performance.get('ranking'):
                    new_best_score = cycle_performance['ranking'][0][1]

                # 4. Vérifier les améliorations
                improvement = new_best_score - current_best_score

                cycle_result = {
                    'cycle': cycle + 1,
                    'improvements': cycle_improvements,
                    'performance': cycle_performance,
                    'best_method': new_best_method,
                    'best_score': new_best_score,
                    'improvement': improvement,
                    'improved': improvement > 0.01  # Seuil d'amélioration significative
                }

                optimization_results['optimization_history'].append(cycle_result)

                if improvement > 0.01:
                    optimization_results['improvements'].append({
                        'cycle': cycle + 1,
                        'improvement': improvement,
                        'new_method': new_best_method,
                        'new_score': new_best_score
                    })
                    current_best_method = new_best_method
                    current_best_score = new_best_score
                    logger.info(f"✅ Amélioration détectée: +{improvement:.3f} avec {new_best_method}")
                else:
                    logger.info(f"📊 Pas d'amélioration significative ce cycle")

                optimization_results['cycles_completed'] = cycle + 1

            # Résultats finaux
            optimization_results['final_performance'] = cycle_performance
            optimization_results['best_configuration'] = {
                'method': current_best_method,
                'score': current_best_score,
                'total_improvement': current_best_score - (initial_performance.get('ranking', [[None, 0]])[0][1] if initial_performance.get('ranking') else 0)
            }

            total_improvement = optimization_results['best_configuration']['total_improvement']

            logger.info(f"🎉 Auto-optimisation terminée!")
            logger.info(f"📈 Amélioration totale: +{total_improvement:.3f}")
            logger.info(f"🏆 Meilleure configuration: {current_best_method} (score: {current_best_score:.3f})")

            return optimization_results

        except Exception as e:
            logger.error(f"Erreur auto-optimisation: {e}")
            optimization_results['error'] = str(e)
            return optimization_results

if __name__ == "__main__":
    # Test du prédicteur
    predictor = KenoPredictor()
    
    print("Test du prédicteur Keno")
    
    # Test avec différentes méthodes
    methods = ['weighted_random', 'hot_numbers', 'balanced', 'pattern_based', 'anti_pattern']
    
    for method in methods:
        print(f"\nMéthode: {method}")
        print(f"Description: {predictor.get_method_description(method)}")
        
        try:
            predictions = predictor.predict(7, method=method, num_sets=3)
            for i, pred in enumerate(predictions, 1):
                confidence = predictor.get_prediction_confidence(pred)
                print(f"  Jeu {i}: {pred} (Confiance: {confidence:.1f}%)")
        except Exception as e:
            print(f"  Erreur: {e}")
